"""
Element matching module for GretahAI ScriptWeaver.

This module provides functions for matching test steps to UI elements using AI.
It implements the match_elements_with_ai function that was previously in core.py.

Key Functions:
- match_elements_with_ai(): Match test steps to UI elements using AI analysis

© 2025 Cogniron All Rights Reserved.
"""

import os
import sys
import json
from debug_utils import debug

# Add the parent directory to the path so we can import from parent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Try to import the AI functions
try:
    from .ai import generate_llm_response, initialize_ai_client
    debug("Successfully imported AI functions",
          stage="match_elements", operation="import_success",
          context={"module": "ai", "functions": ["generate_llm_response", "initialize_ai_client"]})
except ImportError as e:
    debug(f"Error importing AI functions: {e}",
          stage="match_elements", operation="import_error",
          context={"module": "ai", "error": str(e)})

    # Define fallback functions
    def generate_llm_response(prompt, model_name=None):
        debug("Using fallback generate_llm_response function",
              stage="match_elements", operation="fallback_function",
              context={"function": "generate_llm_response"})
        return "{}"

    def initialize_ai_client(api_key=None):
        debug("Using fallback initialize_ai_client function",
              stage="match_elements", operation="fallback_function",
              context={"function": "initialize_ai_client"})
        return False

def match_elements_with_ai(test_cases, elements, api_key=None):
    """
    Match test steps to UI elements using AI.

    Args:
        test_cases (dict or list): Test case(s) to match elements for
        elements (list): List of UI elements to match against
        api_key (str, optional): API key for Google AI. If None, use initialized client

    Returns:
        dict: Dictionary mapping test steps to matched elements
    """
    debug(f"Matching elements for test case(s)",
          stage="match_elements", operation="matching_start",
          context={"test_case_count": len(test_cases) if isinstance(test_cases, list) else 1,
                  "element_count": len(elements) if elements else 0})

    # Handle input format
    if isinstance(test_cases, dict):
        test_cases = [test_cases]
    elif not isinstance(test_cases, list):
        debug(f"Invalid test case format: {type(test_cases)}",
              stage="match_elements", operation="input_validation_error",
              context={"input_type": str(type(test_cases))})
        return {}

    # Check for empty inputs
    if not test_cases or not elements:
        debug("Empty test cases or elements list",
              stage="match_elements", operation="empty_input_warning",
              context={"test_case_count": len(test_cases) if test_cases else 0,
                      "element_count": len(elements) if elements else 0})
        return {}

    # Initialize AI client if API key is provided
    if api_key:
        initialize_ai_client(api_key)
    elif not initialize_ai_client():
        debug("Failed to initialize AI client",
              stage="match_elements", operation="ai_client_init_failed",
              context={"api_key_provided": bool(api_key)})
        return {}

    matches = {}
    for test_case in test_cases:
        try:
            if not isinstance(test_case, dict):
                debug(f"Skipping non-dict test case: {type(test_case)}",
                      stage="match_elements", operation="test_case_type_error",
                      context={"test_case_type": str(type(test_case))})
                continue

            # Get test case ID
            tc_id = test_case.get('Test Case ID') or test_case.get('id')
            if not tc_id:
                debug("Skipping test case with no ID",
                      stage="match_elements", operation="test_case_no_id",
                      context={"test_case_keys": list(test_case.keys()) if isinstance(test_case, dict) else []})
                continue

            matches[tc_id] = {}

            # Get steps from the test case using standardized key
            steps = test_case.get('Steps', [])
            if not isinstance(steps, list):
                debug(f"Skipping test case {tc_id} with invalid steps format: {type(steps)}",
                      stage="match_elements", operation="steps_format_error",
                      context={"test_case_id": tc_id, "steps_type": str(type(steps))})
                continue

            # Process each step
            for i, step in enumerate(steps):
                if not isinstance(step, dict):
                    debug(f"Skipping non-dict step in test case {tc_id}: {type(step)}",
                          stage="match_elements", operation="step_type_error",
                          context={"test_case_id": tc_id, "step_index": i, "step_type": str(type(step))})
                    continue

                # Get step information
                step_num = step.get('Step No') or step.get('step_no') or str(i+1)
                action = step.get('Test Steps') or step.get('action', '')
                expected = step.get('Expected Result') or step.get('expected', '')

                # Create AI prompt for element matching
                prompt = f"""
                Analyze this test step and find matching UI elements if any are needed. If no UI element is required (e.g., navigation or page presence), return an empty matches list.
                Test Case: {tc_id}
                Step {step_num}:
                Action: {action}
                Expected: {expected}
                Available UI Elements (JSON array):
                {json.dumps(elements, indent=2)}
                For each relevant UI element, determine:
                1. How well it matches the step requirements (confidence score 0-1)
                2. What action should be performed (click, input, verify, clear, etc.)
                3. Any specific data or values needed
                Return ONLY valid JSON in this format (no explanation, no markdown):
                {{
                    "matches": [
                        {{
                            "element": {{"element object"}},
                            "score": float,
                            "action": string,
                            "data": string or null
                        }}
                    ]
                }}
                """

                try:
                    # Get AI response
                    response = generate_llm_response(prompt)

                    # Handle empty response
                    if not response or not response.strip():
                        debug(f"Empty response for test case {tc_id}, step {step_num}",
                              stage="match_elements", operation="empty_ai_response",
                              context={"test_case_id": tc_id, "step_num": step_num})
                        matches[tc_id][str(step_num)] = []
                        continue

                    # Clean and parse response using standardized function
                    try:
                        from .ai_helpers import clean_llm_response
                        cleaned_response = clean_llm_response(response, "json")

                        # Parse JSON response
                        matches_data = json.loads(cleaned_response)

                        # Extract matches
                        step_matches = []
                        for match in matches_data.get('matches', []):
                            step_matches.append(match)

                        # Sort matches by score
                        step_matches.sort(key=lambda x: x.get('score', 0), reverse=True)
                        matches[tc_id][str(step_num)] = step_matches

                    except Exception as parse_error:
                        debug(f"Error parsing AI response for test case {tc_id}, step {step_num}: {parse_error}",
                              stage="match_elements", operation="ai_response_parse_error",
                              context={"test_case_id": tc_id, "step_num": step_num, "error": str(parse_error)})
                        matches[tc_id][str(step_num)] = []
                        continue

                except Exception as ai_error:
                    debug(f"Error getting AI response for test case {tc_id}, step {step_num}: {ai_error}",
                          stage="match_elements", operation="ai_response_error",
                          context={"test_case_id": tc_id, "step_num": step_num, "error": str(ai_error)})
                    matches[tc_id][str(step_num)] = []

        except Exception as tc_error:
            debug(f"Error processing test case: {tc_error}",
                  stage="match_elements", operation="test_case_processing_error",
                  context={"error": str(tc_error)})
            continue

    return matches
