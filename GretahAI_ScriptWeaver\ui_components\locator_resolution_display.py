"""
Locator Resolution Display Components for GretahAI ScriptWeaver

This module provides UI components for displaying locator resolution results
in Stage 4, giving users visibility into the element identification strategy
that will be used in test script generation.

Key Features:
- Display resolved locator strategy and value
- Show element attributes captured during selection
- Display fallback locators and alternatives
- Provide confidence scoring and resolution reasoning
- Professional styling with clear visual hierarchy
"""

import streamlit as st
from typing import Dict, List, Any, Optional
from debug_utils import debug


def render_locator_resolution_display(
    resolution_result: Dict[str, Any],
    element_data: Dict[str, Any],
    step_info: Dict[str, Any] = None,
    use_expanders: bool = True
) -> None:
    """
    Render the locator resolution display section.

    Args:
        resolution_result: Result from resolve_locator_conflicts()
        element_data: Selected element data with attributes
        step_info: Optional step information for context
        use_expanders: Whether to use expanders (False for nested display)
    """
    debug("Rendering locator resolution display",
          stage="stage4", operation="ui_display",
          context={
              'has_resolution': bool(resolution_result),
              'has_element_data': bool(element_data),
              'resolved_strategy': resolution_result.get('resolved_locator_strategy', 'unknown')
          })
    
    st.markdown("---")
    st.markdown("### 🎯 Element Identification Strategy")
    st.markdown("*This shows how the selected element will be identified in the generated test script*")
    
    # Create main display columns
    col1, col2 = st.columns([2, 1])
    
    with col1:
        _render_primary_locator_info(resolution_result, element_data)
    
    with col2:
        _render_confidence_and_status(resolution_result)
    
    # Additional details in expandable sections or containers
    if use_expanders:
        with st.expander("📋 Element Attributes", expanded=False):
            _render_element_attributes(element_data)

        with st.expander("🔄 Alternative Locators", expanded=False):
            _render_alternative_locators(resolution_result, element_data)

        with st.expander("🔍 Resolution Details", expanded=False):
            _render_resolution_details(resolution_result, step_info)
    else:
        # Nested mode - use simple containers with headers
        st.markdown("---")

        # Element Attributes section
        st.markdown("#### 📋 Element Attributes")
        with st.container():
            _render_element_attributes(element_data)

        st.markdown("---")

        # Alternative Locators section
        st.markdown("#### 🔄 Alternative Locators")
        with st.container():
            _render_alternative_locators(resolution_result, element_data)

        st.markdown("---")

        # Resolution Details section
        st.markdown("#### 🔍 Resolution Details")
        with st.container():
            _render_resolution_details(resolution_result, step_info)



def _render_primary_locator_info(resolution_result: Dict[str, Any], element_data: Dict[str, Any]) -> None:
    """Render the primary locator strategy and value."""
    strategy = resolution_result.get('resolved_locator_strategy', 'unknown')
    locator = resolution_result.get('resolved_locator', 'N/A')
    
    # Strategy display with icon
    strategy_icons = {
        'id': '🆔',
        'css': '🎨',
        'xpath': '🗂️',
        'name': '📛',
        'class': '🏷️',
        'tag': '🏗️',
        'text': '📝',
        'url': '🌐'
    }
    
    icon = strategy_icons.get(strategy.lower(), '🔍')
    
    st.markdown(f"**Primary Locator Strategy:** {icon} `{strategy.upper()}`")
    
    # Locator value with proper formatting
    if locator and locator != 'N/A':
        # Format based on strategy type
        if strategy.lower() == 'xpath':
            st.code(locator, language='xpath')
        elif strategy.lower() in ['css', 'css_selector']:
            st.code(locator, language='css')
        else:
            st.code(locator)
    else:
        st.markdown("*No specific locator value*")
    
    # Element name for context
    element_name = element_data.get('name', 'Unknown Element')
    st.markdown(f"**Element:** {element_name}")


def _render_confidence_and_status(resolution_result: Dict[str, Any]) -> None:
    """Render confidence score and resolution status."""
    confidence = resolution_result.get('confidence_score', 0.0)
    conflict_detected = resolution_result.get('conflict_detected', False)
    
    # Confidence score with color coding
    st.markdown("**Confidence Score:**")
    
    # Color based on confidence level
    if confidence >= 0.8:
        color = "green"
        status = "High"
    elif confidence >= 0.6:
        color = "orange"
        status = "Medium"
    else:
        color = "red"
        status = "Low"
    
    st.progress(confidence)
    st.markdown(f"<span style='color: {color}'>{status} ({confidence:.1%})</span>", 
                unsafe_allow_html=True)
    
    # Conflict status
    if conflict_detected:
        st.warning("⚠️ Conflicts resolved")
    else:
        st.success("✅ No conflicts")


def _render_element_attributes(element_data: Dict[str, Any]) -> None:
    """Render captured element attributes."""
    attributes = element_data.get('attributes', {})
    
    if not attributes:
        st.info("No attributes captured")
        return
    
    # Group attributes by importance
    primary_attrs = ['id', 'name', 'class', 'type', 'tag']
    content_attrs = ['text', 'value', 'placeholder']
    position_attrs = ['x', 'y', 'width', 'height']
    other_attrs = [k for k in attributes.keys() 
                   if k not in primary_attrs + content_attrs + position_attrs]
    
    # Display primary attributes
    if any(attributes.get(attr) for attr in primary_attrs):
        st.markdown("**Primary Attributes:**")
        for attr in primary_attrs:
            value = attributes.get(attr)
            if value:
                st.markdown(f"- **{attr}:** `{value}`")
    
    # Display content attributes
    content_values = [attributes.get(attr) for attr in content_attrs if attributes.get(attr)]
    if content_values:
        st.markdown("**Content:**")
        for attr in content_attrs:
            value = attributes.get(attr)
            if value:
                # Truncate long text
                display_value = value[:50] + "..." if len(str(value)) > 50 else value
                st.markdown(f"- **{attr}:** `{display_value}`")
    
    # Display position (if available)
    if any(attributes.get(attr) for attr in position_attrs):
        st.markdown("**Position:**")
        x, y = attributes.get('x', 0), attributes.get('y', 0)
        w, h = attributes.get('width', 0), attributes.get('height', 0)
        st.markdown(f"- **Location:** ({x}, {y})")
        st.markdown(f"- **Size:** {w} × {h}")
    
    # Display other attributes
    if other_attrs:
        st.markdown("**Other Attributes:**")
        for attr in other_attrs:
            value = attributes.get(attr)
            if value:
                st.markdown(f"- **{attr}:** `{value}`")


def _render_alternative_locators(resolution_result: Dict[str, Any], element_data: Dict[str, Any]) -> None:
    """Render alternative locator strategies."""
    original_matches = resolution_result.get('original_element_matches', [])
    
    if not original_matches:
        st.info("No alternative locators available")
        return
    
    st.markdown("**Available Alternatives:**")
    
    # Extract unique locator strategies from element matches
    alternatives = []
    for match in original_matches:
        element = match.get('element', {})
        
        # Check for different locator types
        attrs = element.get('attributes', {})
        
        if attrs.get('id'):
            alternatives.append(('ID', attrs['id'], 'High'))
        if element.get('selector'):
            alternatives.append(('CSS Selector', element['selector'], 'Medium'))
        if element.get('xpath'):
            alternatives.append(('XPath', element['xpath'], 'Medium'))
        if attrs.get('name'):
            alternatives.append(('Name', attrs['name'], 'Medium'))
        if attrs.get('class'):
            alternatives.append(('Class', attrs['class'], 'Low'))
    
    # Remove duplicates and display
    seen = set()
    for strategy, locator, reliability in alternatives:
        key = (strategy, locator)
        if key not in seen:
            seen.add(key)
            
            # Reliability indicator
            reliability_color = {
                'High': 'green',
                'Medium': 'orange', 
                'Low': 'red'
            }.get(reliability, 'gray')
            
            st.markdown(f"- **{strategy}:** `{locator}` "
                       f"<span style='color: {reliability_color}'>({reliability})</span>",
                       unsafe_allow_html=True)
    
    if not alternatives:
        st.info("No alternative locators detected")


def _render_resolution_details(resolution_result: Dict[str, Any], step_info: Dict[str, Any] = None) -> None:
    """Render detailed resolution information."""
    reason = resolution_result.get('resolution_reason', 'No reason provided')
    original_step_locator = resolution_result.get('original_step_locator', {})
    
    st.markdown("**Resolution Reasoning:**")
    st.markdown(f"*{reason}*")
    
    # Original step locator (if different)
    if original_step_locator:
        orig_strategy = original_step_locator.get('strategy', '')
        orig_locator = original_step_locator.get('locator', '')
        
        if orig_strategy and orig_strategy.lower() not in ['', 'none', 'n/a']:
            st.markdown("**Original Step Locator:**")
            st.markdown(f"- **Strategy:** `{orig_strategy}`")
            if orig_locator:
                st.markdown(f"- **Value:** `{orig_locator}`")
    
    # Step context (if provided)
    if step_info:
        st.markdown("**Step Context:**")
        step_no = step_info.get('step_no', 'Unknown')
        action = step_info.get('action', 'Unknown')
        st.markdown(f"- **Step:** {step_no}")
        st.markdown(f"- **Action:** {action}")
    
    # Technical details
    st.markdown("**Technical Details:**")
    st.json(resolution_result)


def render_locator_resolution_summary(resolution_result: Dict[str, Any]) -> None:
    """
    Render a compact summary of the locator resolution for quick reference.
    
    Args:
        resolution_result: Result from resolve_locator_conflicts()
    """
    if not resolution_result:
        return
    
    strategy = resolution_result.get('resolved_locator_strategy', 'unknown')
    locator = resolution_result.get('resolved_locator', 'N/A')
    confidence = resolution_result.get('confidence_score', 0.0)
    
    # Compact display
    col1, col2, col3 = st.columns([2, 2, 1])
    
    with col1:
        st.metric("Locator Strategy", strategy.upper())
    
    with col2:
        # Truncate long locators
        display_locator = locator[:30] + "..." if len(str(locator)) > 30 else locator
        st.metric("Locator Value", display_locator)
    
    with col3:
        st.metric("Confidence", f"{confidence:.1%}")
