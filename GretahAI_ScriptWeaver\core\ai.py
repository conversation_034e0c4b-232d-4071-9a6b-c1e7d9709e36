"""
Module for AI integration.
Provides functions for interacting with LLMs and manages prompt templates.
Includes comprehensive logging of AI interactions with detailed metrics and function call tracing.

This module serves as the main entry point for AI functionality and imports specialized modules:
- ai_conversion: Test case to step table conversion
- ai_generation: Test script generation
- ai_validation: Script validation
- ai_optimization: Script optimization
- ai_merging: Script merging
- ai_enhancement: Comment enhancement
"""

import os
import json
import time
import re
import uuid
import inspect
from typing import Optional, Dict, Any, List
import google.generativeai as genai
from datetime import datetime

# Import helper functions and classes from ai_helpers.py
from .ai_helpers import (
    # Core utility functions
    capture_call_stack, log_metrics_to_csv, update_google_usage, markdown_table_to_json, json_to_markdown_table,
    get_by_pos, safe_int, load_config,extract_json_from_response, error_handler,
    extract_markdown_from_response, extract_token_counts, format_call_stack_section, format_metadata_section,
    # Classes (using singleton pattern)
    LoggingManager,
    RequestTracker,
    TokenUsageTracker
)

# Import the modularized prompt builder
from .prompt_builder import generate_test_script_prompt, generate_enhanced_test_script_prompt

# Import specialized AI modules for backward compatibility
from .ai_conversion import (
    convert_test_case_to_step_table,
    generate_step_description,
    ensure_step_descriptions
)
from .ai_validation import (
    validate_generated_script
)
from .ai_optimization import (
    optimize_script_with_ai
)
from .ai_merging import (
    merge_scripts_with_ai
)
from .ai_enhancement import (
    enhance_user_comment_with_ai,
    enhance_generation_comment_with_ai
)
from .ai_generation import (
    generate_test_data_prompt,
    generate_test_cases_data_prompt,
    generate_test_script
)

# Initialize the logging manager (for AI-specific logging infrastructure)
logging_manager = LoggingManager.get_instance()
logger = logging_manager.logger

# Import GRETAH standardized logging
from debug_utils import debug

# Initialize the request tracker
request_tracker = RequestTracker.get_instance()

# Initialize the token usage tracker
token_tracker = TokenUsageTracker.get_instance()

def log_ai_interaction(function_name, prompt, response, model_name="gemini-1.5-flash",
                      request_id=None, parent_request_id=None, context=None,
                      input_tokens=None, output_tokens=None, latency_ms=None,
                      error=None, category="general", related_request_ids=None,
                      capture_stack=True, is_prompt_generation=False):
    """
    Log AI interaction to a file with comprehensive metadata and function call tracing.

    Args:
        function_name (str): Name of the function making the API call
        prompt (str): The prompt sent to the AI
        response (str): The response received from the AI
        model_name (str, optional): The AI model used. Defaults to "gemini-1.5-flash"
        request_id (str, optional): Unique ID for this request. Generated if not provided.
        parent_request_id (str, optional): ID of a parent request if this is a follow-up.
        context (dict, optional): Additional context information about the request.
        input_tokens (int, optional): Number of input tokens used.
        output_tokens (int, optional): Number of output tokens generated.
        latency_ms (float, optional): Request latency in milliseconds.
        error (Exception, optional): Exception if the request failed.
        category (str, optional): Category of the interaction (e.g., "script_generation", "step_table").
        related_request_ids (list, optional): List of related request IDs for cross-referencing.
        capture_stack (bool, optional): Whether to capture and include call stack information.
        is_prompt_generation (bool, optional): Whether this is a prompt generation log (affects how call stack is captured).

    Returns:
        tuple: (filepath, request_id) - Path to the log file and the request ID
    """
    try:
        # Generate a request ID if not provided
        if not request_id:
            request_id = str(uuid.uuid4())

        # Get the log file path
        filepath = logging_manager.get_log_path(category, function_name, request_id, error is not None)

        # Calculate token counts if not provided
        if input_tokens is None and prompt:
            input_tokens = len(prompt.split())

        if output_tokens is None and response and isinstance(response, str):
            output_tokens = len(response.split())

        # Capture call stack information if requested
        call_stack_info = None
        if capture_stack:
            # Determine if this is a prompt generation function
            is_prompt_gen = is_prompt_generation or any(name in function_name for name in [
                'prompt', 'generate_test_script_prompt', 'generate_test_data_prompt',
                'convert_test_case_to_step_table', 'merge_scripts_with_ai'
            ])

            # Capture call stack with appropriate settings
            call_stack_info = capture_call_stack(
                skip_frames=2,  # Skip this function and its caller
                is_prompt_generation=is_prompt_gen
            )

        # Store context information for this request
        request_data = {
            'function_name': function_name,
            'timestamp': datetime.now(),
            'model_name': model_name,
            'parent_request_id': parent_request_id,
            'related_request_ids': related_request_ids or [],
            'context': context or {},
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'latency_ms': latency_ms,
            'category': category,
            'error': str(error) if error else None,
            'filepath': filepath,
            'call_stack': call_stack_info
        }

        # Add the request to the tracker
        request_tracker.add_request(request_id, request_data)

        # Format the metadata section
        content = format_metadata_section(
            request_id, function_name, category, model_name,
            parent_request_id, related_request_ids or [],
            input_tokens, output_tokens, latency_ms, error, context
        )

        # Add function call trace if available
        if call_stack_info:
            content += format_call_stack_section(call_stack_info, function_name, is_prompt_generation)

        # Add prompt and response sections
        content += f"""
==============================================================================
PROMPT:
==============================================================================
{prompt}

==============================================================================
RESPONSE:
==============================================================================
{response}
"""

        # Write to the file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(content)

        # Log metrics to a separate CSV file for analysis
        log_metrics_to_csv(request_id, function_name, category, model_name,
                          input_tokens, output_tokens, latency_ms, error is not None)

        # Update parent request with this child request ID if applicable
        if parent_request_id:
            request_tracker.link_requests(parent_request_id, request_id)
            request_tracker.update_parent_log(parent_request_id, request_id, filepath)

        debug(f"AI interaction logged to {filepath} [Request ID: {request_id}]",
              stage="ai_processing", operation="interaction_logged",
              context={'filepath': filepath, 'request_id': request_id, 'function_name': function_name, 'category': category})
        return filepath, request_id
    except Exception as e:
        debug(f"Error logging AI interaction: {e}",
              stage="error_handling", operation="logging_error",
              context={'error_type': type(e).__name__, 'error_message': str(e), 'function_name': function_name})
        return None, request_id

@error_handler
def initialize_ai_client(api_key=None):
    """
    Initialize the Google AI client.

    Args:
        api_key (str, optional): API key for Google AI. If None, load from config

    Returns:
        bool: True if successful, False otherwise
    """
    # If API key is provided, use it directly
    if not api_key:
        # Try to load from config.json first
        try:
            config = load_config()
            api_key = config.get('google_api_key')
        except Exception as e:
            debug(f"Error loading from config.json: {e}",
                  stage="configuration", operation="config_load_error",
                  context={'config_file': 'config.json', 'error_type': type(e).__name__, 'error_message': str(e)})

        # If still no API key, try scriptweaver_config.json
        if not api_key:
            try:
                # Get the directory where this file is located
                current_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(current_dir)
                config_path = os.path.join(parent_dir, "scriptweaver_config.json")

                if os.path.exists(config_path):
                    with open(config_path, 'r') as f:
                        config = json.load(f)
                        api_key = config.get('google_api_key')
                        debug(f"Loaded API key from scriptweaver_config.json",
                              stage="configuration", operation="api_key_loaded",
                              context={'config_file': 'scriptweaver_config.json', 'config_path': config_path})
            except Exception as e:
                debug(f"Error loading from scriptweaver_config.json: {e}",
                      stage="configuration", operation="config_load_error",
                      context={'config_file': 'scriptweaver_config.json', 'error_type': type(e).__name__, 'error_message': str(e)})

    if not api_key:
        debug("Google API key not found in any config file",
              stage="configuration", operation="api_key_not_found",
              context={'searched_files': ['config.json', 'scriptweaver_config.json']})
        return False

    # Configure the Google AI client
    genai.configure(api_key=api_key)
    debug("Google AI client initialized successfully",
          stage="api_communication", operation="client_initialized",
          context={'api_configured': True})

    # Add to usage tracking if needed
    if 'google_token_usage' not in globals():
        globals()['google_token_usage'] = []

    return True

@error_handler
def generate_llm_response(prompt=None, model_name=None, api_key=None,
                     context=None, parent_request_id=None, category="general",
                     related_request_ids=None, system_prompt=None, user_prompt=None,
                     function_name="generate_llm_response"):
    """
    Generate a response from the LLM based on the provided prompt with comprehensive logging.

    Args:
        prompt (str, optional): The prompt to send to the LLM (legacy parameter)
        model_name (str, optional): The LLM model to use. Defaults to "gemini-1.5-flash"
        api_key (str, optional): API key for Google AI. If None, use initialized client
        context (dict, optional): Additional context information about the request
        parent_request_id (str, optional): ID of a parent request if this is a follow-up
        category (str, optional): Category of the interaction (e.g., "script_generation")
        related_request_ids (list, optional): List of related request IDs for cross-referencing
        system_prompt (str, optional): System prompt for the LLM (for newer models)
        user_prompt (str, optional): User prompt for the LLM (for newer models)
        function_name (str, optional): Name of the function calling this method (for logging)

    Returns:
        str: The LLM response
    """
    request_id = str(uuid.uuid4())
    start_time = time.time()
    input_tokens = None
    output_tokens = None

    # Handle different prompt formats (legacy prompt or system/user prompts)
    if prompt is None and user_prompt is not None:
        # Using the newer system/user prompt format
        prompt = user_prompt  # For logging purposes

        # Check if the model supports system prompts
        if "gemini-2.0" in model_name:
            # Gemini 2.0 models don't support system prompts, so combine them
            if system_prompt:
                # Combine system and user prompts for models that don't support system prompts
                combined_prompt = f"System instructions: {system_prompt}\n\nUser request: {user_prompt}"
                actual_prompt = combined_prompt
            else:
                actual_prompt = user_prompt
        else:
            # For models that support system/user roles
            actual_prompt = {"role": "user", "parts": [user_prompt]}
            if system_prompt:
                actual_prompt = [{"role": "system", "parts": [system_prompt]}, actual_prompt]
    else:
        # Using the legacy single prompt format
        actual_prompt = prompt

    # Add caller information to context
    caller_info = {}
    try:
        frame = inspect.currentframe().f_back
        if frame:
            caller_info = {
                'caller_file': frame.f_code.co_filename,
                'caller_function': frame.f_code.co_name,
                'caller_lineno': frame.f_lineno
            }
    except Exception:
        pass

    if context is None:
        context = {}
    context['caller_info'] = caller_info

    # If model_name is not provided, try to load it from config
    if not model_name:
        try:
            # Try scriptweaver_config.json first
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            config_path = os.path.join(parent_dir, "scriptweaver_config.json")

            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    model_name = config.get('model_name')
                    debug(f"Loaded model_name from scriptweaver_config.json: {model_name}",
                          stage="configuration", operation="model_loaded",
                          context={'config_file': 'scriptweaver_config.json', 'model_name': model_name, 'config_path': config_path})
        except Exception as e:
            debug(f"Error loading model_name from scriptweaver_config.json: {e}",
                  stage="configuration", operation="model_load_error",
                  context={'config_file': 'scriptweaver_config.json', 'error_type': type(e).__name__, 'error_message': str(e)})

        # If still no model_name, try the regular config
        if not model_name:
            try:
                config = load_config()
                model_name = config.get('model_name')
                debug(f"Loaded model_name from config.json: {model_name}",
                      stage="configuration", operation="model_loaded",
                      context={'config_file': 'config.json', 'model_name': model_name})
            except Exception as e:
                debug(f"Error loading model_name from config.json: {e}",
                      stage="configuration", operation="model_load_error",
                      context={'config_file': 'config.json', 'error_type': type(e).__name__, 'error_message': str(e)})

        # If still no model_name, use default
        if not model_name:
            model_name = "gemini-2.0-flash"
            debug(f"Using default model_name: {model_name}",
                  stage="configuration", operation="model_default",
                  context={'model_name': model_name, 'reason': 'no_config_found'})

    # Always explicitly configure the client with the API key (from param or env)
    if not api_key:
        api_key = os.environ.get("GOOGLE_API_KEY")
    if not api_key:
        # Try to initialize the client to get the API key
        if not initialize_ai_client():
            raise ValueError("No Google API key provided or found in any config file.")
        # Try again with environment variable that might have been set by initialize_ai_client
        api_key = os.environ.get("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError("No Google API key provided or found in any config file.")
    genai.configure(api_key=api_key)

    # Configure the model
    model = genai.GenerativeModel(model_name)

    # Generate the response
    debug(f"Sending request to {model_name} [Request ID: {request_id}]",
          stage="api_communication", operation="api_request",
          context={'model_name': model_name, 'request_id': request_id, 'function_name': function_name, 'category': category})
    response = model.generate_content(actual_prompt)

    # Calculate latency
    end_time = time.time()
    latency_ms = (end_time - start_time) * 1000

    # Extract token counts
    input_tokens, output_tokens = extract_token_counts(response, prompt)

    # Track token usage
    token_tracker.add_usage(input_tokens)

    # Update Google usage stats in session state
    update_google_usage(input_tokens)

    # Prepare context information
    request_context = context.copy()
    request_context.update({
        'model': model_name,
        'timestamp': datetime.now().isoformat(),
        'latency_ms': latency_ms,
        'input_tokens': input_tokens,
        'output_tokens': output_tokens,
        'total_tokens': input_tokens + output_tokens if input_tokens and output_tokens else None,
        'has_system_prompt': system_prompt is not None
    })

    # Determine what to log as the prompt
    log_prompt = prompt
    if system_prompt and user_prompt:
        log_prompt = f"SYSTEM PROMPT:\n{system_prompt}\n\nUSER PROMPT:\n{user_prompt}"

    # Log the interaction with enhanced metadata and call stack
    log_ai_interaction(
        function_name=function_name,
        prompt=log_prompt,
        response=response.text,
        model_name=model_name,
        request_id=request_id,
        parent_request_id=parent_request_id,
        related_request_ids=related_request_ids,
        context=request_context,
        input_tokens=input_tokens,
        output_tokens=output_tokens,
        latency_ms=latency_ms,
        category=category,
        capture_stack=True
    )

    debug(f"LLM response generated successfully in {latency_ms:.2f}ms [Request ID: {request_id}]",
          stage="api_communication", operation="api_response_success",
          context={'model_name': model_name, 'request_id': request_id, 'latency_ms': latency_ms,
                   'input_tokens': input_tokens, 'output_tokens': output_tokens, 'function_name': function_name})

    # Return the text
    return response.text

# Functions moved to specialized modules - keeping imports for backward compatibility

# convert_test_case_to_step_table moved to ai_conversion.py
# _validate_and_fix_first_step_navigation moved to ai_conversion.py
# generate_test_data_prompt and generate_test_cases_data_prompt moved to ai_generation.py
# _calculate_fallback_quality_score and validate_generated_script moved to ai_validation.py
# optimize_script_with_ai moved to ai_optimization.py
# enhance_user_comment_with_ai moved to ai_enhancement.py
# _build_generation_prompts moved to ai_enhancement.py
# enhance_generation_comment_with_ai moved to ai_enhancement.py
# merge_scripts_with_ai moved to ai_merging.py
# generate_test_script moved to ai_generation.py
