"""
Stage 1: Upload Excel File

This module handles the Excel file upload and parsing functionality.
Maintains the StateManager pattern and follows the established architectural patterns.

Phase 3b Enhancement: Standardized logging with centralized infrastructure
"""

import os
import logging
import tempfile
import streamlit as st
import pandas as pd
from pathlib import Path
from state_manager import StateStage

# Enhanced logging configuration using centralized infrastructure
from core.logging_config import get_stage_logger
logger = get_stage_logger("stage1")

# Import helper functions from other modules
from core.excel_parser import parse_excel
from debug_utils import debug

def validate_uploaded_file(uploaded_file, file_content):
    """
    Validate uploaded file before processing.

    Args:
        uploaded_file: Streamlit uploaded file object
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of validation errors (empty if valid)
    """
    debug("Starting file validation",
          stage="stage1",
          operation="file_validation",
          context={
              'filename': uploaded_file.name,
              'file_size_bytes': len(file_content),
              'file_size_mb': round(len(file_content) / (1024 * 1024), 2)
          })

    errors = []

    # File size validation (50MB limit)
    if len(file_content) == 0:
        error_msg = "File is empty"
        errors.append(error_msg)
        debug(f"File validation failed: {error_msg}",
              stage="stage1",
              operation="file_validation_error",
              context={'error_type': 'empty_file', 'filename': uploaded_file.name})
    elif len(file_content) > 50 * 1024 * 1024:
        error_msg = "File too large (max 50MB)"
        errors.append(error_msg)
        debug(f"File validation failed: {error_msg}",
              stage="stage1",
              operation="file_validation_error",
              context={
                  'error_type': 'file_too_large',
                  'filename': uploaded_file.name,
                  'file_size_mb': round(len(file_content) / (1024 * 1024), 2)
              })

    # File extension validation
    if not uploaded_file.name.lower().endswith('.xlsx'):
        error_msg = "Invalid file extension (must be .xlsx)"
        errors.append(error_msg)
        debug(f"File validation failed: {error_msg}",
              stage="stage1",
              operation="file_validation_error",
              context={
                  'error_type': 'invalid_extension',
                  'filename': uploaded_file.name,
                  'actual_extension': os.path.splitext(uploaded_file.name)[1]
              })

    # Basic Excel file signature check
    if len(file_content) >= 2 and not file_content.startswith(b'PK'):
        error_msg = "File does not appear to be a valid Excel file"
        errors.append(error_msg)
        debug(f"File validation failed: {error_msg}",
              stage="stage1",
              operation="file_validation_error",
              context={
                  'error_type': 'invalid_signature',
                  'filename': uploaded_file.name,
                  'file_signature': file_content[:4].hex() if len(file_content) >= 4 else 'too_short'
              })

    if not errors:
        debug("File validation successful",
              stage="stage1",
              operation="file_validation_success",
              context={
                  'filename': uploaded_file.name,
                  'file_size_mb': round(len(file_content) / (1024 * 1024), 2)
              })

    return errors

def safe_get_test_case_count(test_cases):
    """
    Safely get test case count with validation.

    Args:
        test_cases: Test cases data structure

    Returns:
        int: Number of test cases (0 if invalid)
    """
    if not test_cases:
        debug("Test cases data is empty or None",
              stage="stage1",
              operation="test_case_count_validation",
              context={'test_cases_type': type(test_cases).__name__, 'is_none': test_cases is None})
        return 0

    if not isinstance(test_cases, list):
        debug(f"Warning: test_cases is not a list, type: {type(test_cases)}",
              stage="stage1",
              operation="test_case_count_validation_error",
              context={
                  'expected_type': 'list',
                  'actual_type': type(test_cases).__name__,
                  'data_preview': str(test_cases)[:100] if test_cases else 'None'
              })
        return 0

    count = len(test_cases)
    debug(f"Test case count validated: {count}",
          stage="stage1",
          operation="test_case_count_validation_success",
          context={'count': count, 'data_type': 'list'})
    return count

def validate_stage1_completion(state):
    """
    Validate Stage 1 completion criteria.

    Args:
        state: StateManager instance

    Returns:
        tuple: (is_valid: bool, message: str)
    """
    debug("Starting Stage 1 completion validation",
          stage="stage1",
          operation="completion_validation")

    validation_context = {
        'has_test_cases_attr': hasattr(state, 'test_cases'),
        'has_uploaded_excel_attr': hasattr(state, 'uploaded_excel'),
        'test_cases_count': safe_get_test_case_count(getattr(state, 'test_cases', None))
    }

    if not hasattr(state, 'test_cases') or not state.test_cases:
        error_msg = "No test cases loaded"
        debug(f"Stage 1 completion validation failed: {error_msg}",
              stage="stage1",
              operation="completion_validation_error",
              context={**validation_context, 'error_type': 'no_test_cases'})
        return False, error_msg

    if not hasattr(state, 'uploaded_excel') or not state.uploaded_excel:
        error_msg = "No Excel file uploaded"
        debug(f"Stage 1 completion validation failed: {error_msg}",
              stage="stage1",
              operation="completion_validation_error",
              context={**validation_context, 'error_type': 'no_uploaded_file'})
        return False, error_msg

    if not os.path.exists(state.uploaded_excel):
        error_msg = "Uploaded file no longer exists"
        debug(f"Stage 1 completion validation failed: {error_msg}",
              stage="stage1",
              operation="completion_validation_error",
              context={
                  **validation_context,
                  'error_type': 'file_not_found',
                  'file_path': state.uploaded_excel
              })
        return False, error_msg

    success_msg = "Stage 1 completed successfully"
    debug(f"Stage 1 completion validation successful: {success_msg}",
          stage="stage1",
          operation="completion_validation_success",
          context={
              **validation_context,
              'file_path': state.uploaded_excel,
              'file_exists': True
          })
    return True, success_msg

@st.cache_data
def parse_excel_cached(file_content):
    """
    Cached version of parse_excel function with improved resource management.

    Args:
        file_content (bytes): Raw content of the Excel file

    Returns:
        list: List of test cases
    """
    debug("Using cached parse_excel function",
          stage="stage1",
          operation="excel_parsing_cached",
          context={'file_size_bytes': len(file_content)})

    temp_file_path = None
    parsing_start_time = None

    try:
        # Create a temporary file to pass to parse_excel
        debug("Creating temporary file for Excel parsing",
              stage="stage1",
              operation="temp_file_creation")

        with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        debug(f"Temporary file created: {temp_file_path}",
              stage="stage1",
              operation="temp_file_created",
              context={'temp_file_path': temp_file_path, 'file_size_bytes': len(file_content)})

        # Parse the Excel file using the existing function
        import time
        parsing_start_time = time.time()

        debug("Starting Excel file parsing",
              stage="stage1",
              operation="excel_parsing_start",
              context={'temp_file_path': temp_file_path})

        test_cases = parse_excel(temp_file_path)

        parsing_duration = time.time() - parsing_start_time
        test_case_count = safe_get_test_case_count(test_cases)

        debug(f"Excel parsing completed successfully",
              stage="stage1",
              operation="excel_parsing_success",
              context={
                  'parsing_duration_seconds': round(parsing_duration, 3),
                  'test_cases_found': test_case_count,
                  'temp_file_path': temp_file_path
              })

        return test_cases

    except Exception as e:
        parsing_duration = time.time() - parsing_start_time if parsing_start_time else 0
        debug(f"Error in cached parse_excel: {e}",
              stage="stage1",
              operation="excel_parsing_error",
              context={
                  'error_type': type(e).__name__,
                  'error_message': str(e),
                  'parsing_duration_seconds': round(parsing_duration, 3) if parsing_duration else 0,
                  'temp_file_path': temp_file_path
              })
        logger.error(f"Error in cached parse_excel: {e}")
        raise
    finally:
        # Ensure cleanup even if parsing fails
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                debug(f"Temporary file cleaned up successfully: {temp_file_path}",
                      stage="stage1",
                      operation="temp_file_cleanup_success",
                      context={'temp_file_path': temp_file_path})
            except Exception as cleanup_error:
                debug(f"Warning: Failed to cleanup temp file {temp_file_path}: {cleanup_error}",
                      stage="stage1",
                      operation="temp_file_cleanup_error",
                      context={
                          'temp_file_path': temp_file_path,
                          'cleanup_error': str(cleanup_error)
                      })

def stage1_upload_excel(state):
    """Phase 1: Upload Excel File."""
    debug("Stage 1: Excel upload interface accessed",
          stage="stage1",
          operation="stage_entry",
          context={'current_stage': state.current_stage.name if hasattr(state, 'current_stage') else 'unknown'})

    st.markdown("<h2 class='stage-header'>Phase 1: Upload Test Case Excel</h2>", unsafe_allow_html=True)

    # Help text in an expander to reduce visual clutter
    with st.expander("About Excel Format", expanded=False):
        st.markdown("""
        Upload an Excel file with the following columns:
        - **Test Case ID**: Unique identifier
        - **Test Case Objective**: Description of what is being tested
        - **Step No**: Step number
        - **Test Steps**: Action to perform
        - **Expected Result**: Expected outcome
        """)

    # Simplified file uploader with clearer label
    uploaded_file = st.file_uploader("Select Excel file (.xlsx)", type=["xlsx"], key="excel_uploader")

    debug("File uploader rendered",
          stage="stage1",
          operation="ui_render",
          context={'uploader_key': 'excel_uploader', 'accepted_types': ['xlsx']})

    if uploaded_file is not None:
        debug(f"File uploaded: {uploaded_file.name}",
              stage="stage1",
              operation="file_upload",
              context={
                  'filename': uploaded_file.name,
                  'file_size_bytes': len(uploaded_file.getvalue()),
                  'file_type': uploaded_file.type
              })

        try:
            # Get the file content
            file_content = uploaded_file.getvalue()

            debug("File content retrieved",
                  stage="stage1",
                  operation="file_content_retrieval",
                  context={
                      'filename': uploaded_file.name,
                      'content_size_bytes': len(file_content),
                      'content_size_mb': round(len(file_content) / (1024 * 1024), 2)
                  })

            # Validate uploaded file before processing
            validation_errors = validate_uploaded_file(uploaded_file, file_content)
            if validation_errors:
                debug(f"File validation failed with {len(validation_errors)} errors",
                      stage="stage1",
                      operation="file_validation_failed",
                      context={
                          'filename': uploaded_file.name,
                          'error_count': len(validation_errors),
                          'errors': validation_errors
                      })
                for error in validation_errors:
                    st.error(f"❌ {error}")
                return

            # Check if this is the same file we've already processed
            current_hash = hash(file_content)
            if hasattr(state, 'last_file_content_hash') and state.last_file_content_hash == current_hash:
                debug("File content unchanged - skipping reprocessing",
                      stage="stage1",
                      operation="file_content_unchanged",
                      context={
                          'filename': uploaded_file.name,
                          'content_hash': current_hash,
                          'skip_processing': True
                      })
                logger.info("File content unchanged - skipping reprocessing")
                # Still show success message and preview for user feedback
                st.success(f"✅ File already processed: {uploaded_file.name}")
            else:
                debug("New or changed file detected - processing",
                      stage="stage1",
                      operation="file_content_changed",
                      context={
                          'filename': uploaded_file.name,
                          'new_hash': current_hash,
                          'old_hash': getattr(state, 'last_file_content_hash', None),
                          'requires_processing': True
                      })
                logger.info("New or changed file detected - processing")
                # Update the content hash in state
                old_hash = getattr(state, 'last_file_content_hash', None)
                state.last_file_content_hash = current_hash
                debug(f"State change: last_file_content_hash = {current_hash} (was: {old_hash})",
                      stage="stage1",
                      operation="state_update",
                      context={
                          'field': 'last_file_content_hash',
                          'new_value': current_hash,
                          'old_value': old_hash
                      })

                # Save the uploaded file to a temporary location
                temp_dir = Path("temp_uploads")
                temp_dir.mkdir(exist_ok=True)

                # Use a consistent filename based on the uploaded file name instead of timestamp
                safe_filename = ''.join(c if c.isalnum() else '_' for c in uploaded_file.name)
                temp_file_path = temp_dir / f"test_cases_{safe_filename}"

                with open(temp_file_path, "wb") as f:
                    f.write(file_content)

                # Update state with file information
                old_excel_path = getattr(state, 'uploaded_excel', None)
                state.uploaded_excel = str(temp_file_path)
                state.uploaded_file = str(temp_file_path)  # Backward compatibility
                debug("Excel file path updated", stage="stage1", operation="file_upload",
                      context={'new_path': state.uploaded_excel, 'old_path': old_excel_path})

                # Process file in a collapsible section
                with st.expander("Processing Results", expanded=True):
                    # Verify the file was saved correctly
                    if os.path.exists(temp_file_path) and os.path.getsize(temp_file_path) > 0:
                        st.success(f"✅ File uploaded: {uploaded_file.name}")

                        # Parse the excel file using the cached function
                        if parse_excel:
                            try:
                                old_test_cases_count = safe_get_test_case_count(getattr(state, 'test_cases', None))
                                state.test_cases = parse_excel_cached(file_content)
                                new_test_cases_count = safe_get_test_case_count(state.test_cases)
                                debug("Test cases parsed from Excel", stage="stage1", operation="file_processing",
                                      context={'new_count': new_test_cases_count, 'old_count': old_test_cases_count})

                                if new_test_cases_count == 0:
                                    st.warning("⚠️ No test cases found. Check file format.")
                                else:
                                    st.success(f"✅ Parsed {new_test_cases_count} test cases")

                                    # Only advance to Stage 2 if we're currently in Stage 1
                                    if state.current_stage == StateStage.STAGE1_UPLOAD:
                                        # Validate completion before advancing
                                        is_valid, validation_message = validate_stage1_completion(state)

                                        if is_valid:
                                            # Use centralized stage advancement with proper state persistence
                                            success = state.advance_to(StateStage.STAGE2_WEBSITE, f"Successfully loaded {new_test_cases_count} test cases")

                                            if success:
                                                # Force state update in session state
                                                st.session_state['state'] = state
                                                st.session_state['stage_progression_message'] = f"✅ {validation_message}. Proceeding to Website Configuration."

                                                # Log successful transition for debugging
                                                debug("Stage transition successful", stage="stage1", operation="stage_transition",
                                                      context={'from_stage': 'stage1', 'to_stage': 'stage2', 'test_cases': new_test_cases_count})
                                                logger.info(f"Stage 1 -> Stage 2 transition successful, test_cases count: {new_test_cases_count}")

                                                # Rerun to show Stage 2 immediately
                                                st.rerun()
                                                return  # Exit early since rerun will restart the function
                                            else:
                                                debug("Stage transition failed", stage="stage1", operation="error_handling")
                                                logger.error("Failed to advance from Stage 1 to Stage 2")
                                                st.error("❌ Failed to advance to next stage. Please try again.")
                                        else:
                                            st.warning(f"⚠️ Cannot proceed: {validation_message}")
                            except Exception as e:
                                debug("Error parsing Excel file", stage="stage1", operation="error_handling",
                                      context={'error': str(e), 'error_type': type(e).__name__})
                                st.error(f"❌ Error parsing file: {e}")
                                state.test_cases = None # Ensure it's reset on error
                        else:
                            st.warning("⚠️ Excel parsing function not available")
                    else:
                        st.error("❌ Failed to save file")

            # Always display a preview of the Excel file (using the cached file if available)
            if hasattr(state, 'uploaded_excel') and os.path.exists(state.uploaded_excel):
                try:
                    df = pd.read_excel(state.uploaded_excel) # Read from the saved temp file
                    with st.expander("📊 File Preview", expanded=True):
                        # Show essential metric prominently
                        if hasattr(state, 'test_cases'):
                            test_case_count = safe_get_test_case_count(state.test_cases)
                            if test_case_count > 0:
                                st.success(f"✅ **{test_case_count} test cases** successfully parsed")
                            else:
                                st.warning("⚠️ No test cases found in file")

                        # Clean, focused data preview
                        st.caption("First 10 rows:")
                        st.dataframe(df.head(10), use_container_width=True, hide_index=True)
                except Exception as e:
                    debug("Error reading file for preview", stage="stage1", operation="error_handling",
                          context={'error': str(e), 'error_type': type(e).__name__})
                    st.error(f"❌ Error reading file: {e}")
        except Exception as e:
            debug("Error processing uploaded file", stage="stage1", operation="error_handling",
                  context={'error': str(e), 'error_type': type(e).__name__})
            st.error(f"❌ Error processing file: {e}")
