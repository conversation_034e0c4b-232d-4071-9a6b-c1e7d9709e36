"""
UI Components for GretahAI ScriptWeaver

This package contains reusable UI components for the ScriptWeaver application.
"""

from .script_editor import render_script_editor, render_script_status_badge, render_script_editor_help
from .locator_resolution_display import (
    render_locator_resolution_display,
    render_locator_resolution_summary
)
from .stage10_components import (
    render_empty_playground_message,
    render_no_test_cases_message,
    render_template_selection_interface,
    render_test_case_selection_interface,
    render_script_generation_controls,
    render_script_execution_section_header,
    render_script_info_card,
    render_execution_controls_header,
    render_verbose_mode_checkbox,
    render_execution_status_indicator,
    render_execution_action_buttons,
    render_execution_results_header,
    render_execution_results_summary,
    render_execution_metrics_header,
    render_junit_metrics_grid,
    render_execution_output_section,
    render_execution_artifacts_section,
    render_stage10_footer,
    render_workflow_navigation,
    render_generation_success_display
)

__all__ = [
    'render_script_editor',
    'render_script_status_badge',
    'render_script_editor_help',
    'render_locator_resolution_display',
    'render_locator_resolution_summary',
    # Stage 10 components
    'render_empty_playground_message',
    'render_no_test_cases_message',
    'render_template_selection_interface',
    'render_test_case_selection_interface',
    'render_script_generation_controls',
    'render_script_execution_section_header',
    'render_script_info_card',
    'render_execution_controls_header',
    'render_verbose_mode_checkbox',
    'render_execution_status_indicator',
    'render_execution_action_buttons',
    'render_execution_results_header',
    'render_execution_results_summary',
    'render_execution_metrics_header',
    'render_junit_metrics_grid',
    'render_execution_output_section',
    'render_execution_artifacts_section',
    'render_stage10_footer',
    'render_workflow_navigation',
    'render_generation_success_display'
]
