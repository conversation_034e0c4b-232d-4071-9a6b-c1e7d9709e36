"""
GretahAI ScriptWeaver - Test Script Generator Application

This module provides a Streamlit application for generating test scripts from Excel test cases.
The application follows a modular architecture with:

1. Centralized State Management:
   - Uses a StateManager dataclass to hold all session state
   - Provides helper methods for state mutations
   - Maintains consistent state across application stages

2. Modular UI Organization:
   - Splits UI logic into smaller stage-specific functions
   - Each stage function accepts and updates the state manager
   - Private helper functions handle repeated logic

3. Clean Separation of Concerns:
   - Pure helper functions extracted to separate modules
   - AI-related logic isolated in core/ai.py
   - UI components and constants in dedicated files

The application workflow:
1. Upload an Excel file containing test cases
2. Provide a website URL to extract UI elements
3. Use Google AI to convert test cases to automation-ready step tables
4. Detect and match UI elements to test steps
5. Configure test data for each step
6. Generate executable PyTest scripts
7. Execute the tests and capture results

© 2025 Cogniron All Rights Reserved.
"""

import os
import sys
import json
import streamlit as st
from datetime import datetime

# Import GRETAH standardized logging
from debug_utils import debug

# Set page config FIRST before any other Streamlit commands
st.set_page_config(
    page_title="GretahAI ScriptWeaver",
    page_icon="🧪",
    layout="wide",
    initial_sidebar_state="expanded",  # Always start with sidebar expanded
)

# --- Ensure GOOGLE_API_KEY is set from config.json before any LLM/genai usage ---
CONFIG_PATH = os.path.join(os.path.dirname(__file__), '..', 'config.json')
try:
    with open(CONFIG_PATH, 'r') as f:
        config = json.load(f)
        google_api_key = config.get('google_api_key')
        if (google_api_key):
            os.environ['GOOGLE_API_KEY'] = google_api_key
except Exception:
    pass

# Then add custom styling with st.markdown
st.markdown(
    """
    <style>
    /* --- Sidebar: purple background, white text --- */
    section[data-testid="stSidebar"] {
        background-color: #673AB7 !important;
    }
    section[data-testid="stSidebar"] *,
    section[data-testid="stSidebar"] .css-1lcbmhc {  /* catch headings, labels, etc. */
        color: #fff !important;
    }
    /* divider line in a slightly lighter purple */
    section[data-testid="stSidebar"] hr {
        border-color: #5E35B1 !important;
    }
    /* make metrics stand out */
    section[data-testid="stSidebar"] .stMetric,
    section[data-testid="stSidebar"] .stMetric .value {
        color: #fff !important;
    }
    </style>
    """,
    unsafe_allow_html=True,
)

# Add the parent directory to the path so we can import from parent modules
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(parent_dir)

# Define the directory where app.py and other files are located
current_app_dir = os.path.dirname(os.path.abspath(__file__))
APP_CONFIG_FILE = os.path.join(current_app_dir, "config.json")
sys.path.insert(0, current_app_dir)  # Add to front to prioritize for dynamic imports

# Import stage functions from the new modular structure
from stages import (
    stage1_upload_excel,
    stage2_enter_website,
    stage3_convert_test_case,
    stage4_ui_detection_and_matching,
    stage5_test_data,
    stage6_generate_script,
    stage7_run_script,
    stage8_optimize_script,
    stage9_browse_scripts,
    stage10_script_playground,
    advance_to_next_step
)

# Import new mode-based navigation
from core.mode_navigation import render_mode_navigation

# Helper Functions for Defensive Programming
def safe_len(value, default=0):
    """
    Safely get the length of a value, handling None values.

    Args:
        value: The value to get the length of (can be None, string, list, dict, etc.)
        default: The default value to return if value is None

    Returns:
        int: The length of the value, or default if value is None
    """
    return len(value) if value is not None else default

# UI Helper Functions
def _show_debug_info(state):
    """
    Display debug information about the application state.

    This function creates an expandable section with detailed state information
    and debug controls for developers.

    Args:
        state: The StateManager instance
    """
    with st.expander("Debug Information (for developers)", expanded=False):
        # Create tabs for different debug sections
        debug_tab1, debug_tab2, debug_tab3, debug_tab4 = st.tabs(["State Variables", "Step Progression", "Workflow Hierarchy", "Session Info"])

        with debug_tab1:
            st.markdown("### State Variables")
            st.write("These variables control the 'Proceed to Next Step' button:")

            # Display key state variables as individual metrics for better visibility
            col1, col2 = st.columns(2)

            with col1:
                st.metric("current_step_index", str(state.current_step_index))
                st.metric("total_steps", str(state.total_steps))
                st.metric("step_ready_for_script", str(state.step_ready_for_script))
                st.metric("all_steps_done", str(state.all_steps_done))

            with col2:
                # Display additional state information if needed
                st.metric("Current Step", f"{state.current_step_index + 1} of {state.total_steps}")
                st.metric("Progress", f"{((state.current_step_index + 1) / max(1, state.total_steps) * 100):.1f}%")

                # Display completed steps if available
                if hasattr(state, 'completed_steps') and isinstance(state.completed_steps, list):
                    st.metric("Completed Steps", f"{len(state.completed_steps)} of {state.total_steps}")

            # Display additional information about the next step if available
            next_step_index = state.current_step_index + 1
            if next_step_index < state.total_steps and state.step_table_json:
                next_step = state.step_table_json[next_step_index]
                next_step_no = next_step.get('step_no', 'N/A')
                next_step_action = next_step.get('action', 'N/A')
                st.info(f"Next step info: Step {next_step_no} - {next_step_action}")

        with debug_tab2:
            # Display debug information about step progression
            st.markdown("### Step Progression Debug")

            # Show button click info if available
            if 'proceed_button_info' in st.session_state:
                st.write("Last 'Proceed to Next Step' button click:")
                st.json(st.session_state['proceed_button_info'])

            # Show state before and after advancement
            col1, col2 = st.columns(2)

            with col1:
                if 'debug_before_advance' in st.session_state:
                    st.write("State before last advance:")
                    st.json(st.session_state['debug_before_advance'])

            with col2:
                if 'debug_after_advance' in st.session_state:
                    st.write("State after last advance:")
                    st.json(st.session_state['debug_after_advance'])

            # Show step context if available
            if hasattr(state, 'step_context') and state.step_context:
                st.markdown("#### Step Context (saved state from previous steps)")
                # Create a toggle using a checkbox
                show_step_context = st.checkbox("Show step context data", value=False, key="show_step_context_debug")
                if show_step_context:
                    st.json(state.step_context)

        with debug_tab3:
            # Display information about the hierarchical workflow
            st.markdown("### Workflow Hierarchy Debug")

            # Suite level
            st.markdown("#### Suite Level (Stage 1)")
            if hasattr(state, 'test_cases') and state.test_cases:
                st.success(f"✅ Suite loaded with {len(state.test_cases)} test cases")
            else:
                st.warning("⚠️ No test cases loaded")

            # Test case level
            st.markdown("#### Test Case Level (Stage 3)")
            if hasattr(state, 'selected_test_case') and state.selected_test_case:
                st.success(f"✅ Test case selected: {state.selected_test_case.get('Test Case ID')}")

                # Show conversion status
                if hasattr(state, 'conversion_done') and state.conversion_done:
                    st.success("✅ Test case converted to step table")
                    if hasattr(state, 'step_table_json') and state.step_table_json:
                        st.info(f"Step table has {len(state.step_table_json)} steps")
                else:
                    st.warning("⚠️ Test case not converted")
            else:
                st.warning("⚠️ No test case selected")

            # Step level
            st.markdown("#### Step Level (Stages 4-7)")
            if hasattr(state, 'selected_step') and state.selected_step:
                st.success(f"✅ Step selected: Step {state.selected_step.get('Step No')}")

                # Show step progress
                step_status = []
                if hasattr(state, 'step_matches') and state.step_matches:
                    step_status.append("✅ UI Elements matched")
                else:
                    step_status.append("⚠️ UI Elements not matched")

                if hasattr(state, 'test_data') and state.test_data:
                    step_status.append("✅ Test data configured")
                elif hasattr(state, 'test_data_skipped') and state.test_data_skipped:
                    step_status.append("✅ Test data skipped")
                else:
                    step_status.append("⚠️ Test data not configured")

                if hasattr(state, 'generated_script_path') and state.generated_script_path:
                    step_status.append("✅ Script generated")
                else:
                    step_status.append("⚠️ Script not generated")

                for status in step_status:
                    st.write(status)
            else:
                st.warning("⚠️ No step selected")

        with debug_tab4:
            # Display session information
            st.markdown("### Session Information")

            try:
                from debug_utils import get_session_info
                session_info = get_session_info()

                col1, col2 = st.columns(2)

                with col1:
                    st.metric("Session ID", session_info.get('session_id', 'unknown'))
                    st.metric("Debug Mode", session_info.get('debug_mode', 'unknown'))
                    st.metric("Console Output", session_info.get('console_output', 'unknown'))

                with col2:
                    st.metric("UI Output", session_info.get('ui_output', 'unknown'))
                    log_file = session_info.get('log_file', 'unknown')
                    if log_file != 'unknown':
                        # Show just the filename for better display
                        log_filename = log_file.split('/')[-1] if '/' in log_file else log_file.split('\\')[-1]
                        st.metric("Log File", log_filename)
                    else:
                        st.metric("Log File", "unknown")

                # Show full log file path in expandable section
                if log_file != 'unknown':
                    with st.expander("Full Log File Path", expanded=False):
                        st.code(log_file)

                        # Add button to view recent log entries
                        if st.button("Show Recent Log Entries", key="show_recent_logs"):
                            try:
                                from pathlib import Path
                                log_path = Path(log_file)
                                if log_path.exists():
                                    # Read last 50 lines of the log file
                                    with open(log_path, 'r', encoding='utf-8') as f:
                                        lines = f.readlines()
                                        recent_lines = lines[-50:] if len(lines) > 50 else lines

                                    st.text_area(
                                        "Recent Log Entries (last 50 lines)",
                                        value=''.join(recent_lines),
                                        height=300,
                                        key="recent_log_display"
                                    )
                                else:
                                    st.warning("Log file not found")
                            except Exception as e:
                                st.error(f"Error reading log file: {e}")

                # Show any errors in session info
                if 'error' in session_info:
                    st.error(f"Session info error: {session_info['error']}")

            except Exception as e:
                st.error(f"Failed to load session information: {e}")

        # Add debug actions in columns
        st.markdown("### Debug Actions")
        debug_col1, debug_col2 = st.columns(2)

        with debug_col1:
            # Add a button to reset the state
            if st.button("Reset State Variables", key="reset_state_debug"):
                # Ask for confirmation
                confirm = st.checkbox("Confirm Reset (this will clear all progress)", key="confirm_debug_reset")

                if confirm:
                    # Use the state manager's methods to reset state
                    state.update_step_progress(
                        step_ready_for_script=False,
                        script_just_generated=False
                    )

                    # Reset step-specific state
                    state.reset_step_state(confirm=True, reason="Debug reset requested by user")

                    st.success("State variables reset. Refresh the page to see the changes.")

                    # Force state update in session state
                    st.session_state['state'] = state
                    st.rerun()
                else:
                    st.warning("Please confirm the reset by checking the box above.")

            # Add a button to force step_ready_for_script flag
            if st.button("Set Ready for Script", key="set_ready_debug"):
                # Use the state manager's update method
                state.update_step_progress(step_ready_for_script=True)
                st.success("Set step_ready_for_script = True. Refresh the page to see the changes.")

                # Force state update in session state
                st.session_state['state'] = state
                st.rerun()

        with debug_col2:
            # Add a direct manual advance function
            if st.button("Manually Advance to Next Step", key="manual_advance_debug"):
                # Call advance_to_next_step directly
                print("Debug panel: Manually advancing to next step")
                # advance_to_next_step() will call st.rerun() internally if successful
                advance_to_next_step()
                # The code below will only execute if advance_to_next_step() fails
                # and doesn't trigger a rerun
                st.error("Failed to advance to next step. See console output for details.")

            # Add a button to force automatic advancement
            if st.button("Force Auto-Advance After Test", key="force_auto_advance_debug"):
                # Set the necessary flags and call the automatic advancement logic
                state.step_ready_for_script = True

                # Check if there are more steps
                next_step_index = state.current_step_index + 1
                if next_step_index < state.total_steps:
                    # Get the next step information
                    next_step = state.step_table_json[next_step_index]
                    next_step_no = next_step.get('step_no', 'N/A')

                    # Store current step completion status
                    current_step_no = state.selected_step.get('Step No')
                    if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                        state.completed_steps = []
                    if current_step_no not in state.completed_steps:
                        state.completed_steps.append(current_step_no)

                    # Set up session state variables before calling advance_to_next_step
                    # since it will trigger a rerun if successful
                    st.session_state['stage_progression_message'] = f"✅ Forced advancement to Step {next_step_no}"

                    # Set a flag to force a refresh after advancement
                    from datetime import datetime
                    st.session_state['force_refresh_after_advance'] = {
                        'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                        'target_step': next_step_no,
                        'from_step': current_step_no
                    }

                    # Call advance_to_next_step to move to the next step
                    # This will call st.rerun() internally if successful
                    advance_to_next_step()

                    # The code below will only execute if advance_to_next_step() fails
                    # and doesn't trigger a rerun
                    st.error("Failed to force automatic advancement. See console output for details.")
                else:
                    st.warning("No more steps to advance to.")

                # Force state update in session state
                st.session_state['state'] = state
                st.rerun()

            # Add a button to fix step selection issues
            if st.button("Fix Step Selection Issues", key="fix_step_selection_debug"):
                # This will force the UI to show the correct step based on current_step_index
                if hasattr(state, 'step_table_json') and state.step_table_json:
                    # Get the current step information
                    if 0 <= state.current_step_index < len(state.step_table_json):
                        current_step = state.step_table_json[state.current_step_index]
                        step_no = current_step.get('step_no', 'N/A')

                        # Find the corresponding original step
                        if hasattr(state, 'selected_test_case') and state.selected_test_case:
                            original_steps = state.selected_test_case.get('Steps', [])
                            selected_original_step = next(
                                (step for step in original_steps if str(step.get('Step No')) == str(step_no)),
                                None
                            )

                            if selected_original_step:
                                # Update the selected step
                                state.selected_step = selected_original_step
                                state.selected_step_table_entry = current_step

                                # Set a flag to force a refresh
                                from datetime import datetime
                                st.session_state['force_refresh_after_advance'] = {
                                    'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                                    'target_step': step_no,
                                    'from_step': 'unknown'
                                }

                                st.success(f"Fixed step selection to show Step {step_no} (index {state.current_step_index})")
                            else:
                                st.error(f"Could not find original step for step number {step_no}")
                        else:
                            st.error("No test case selected")
                    else:
                        st.error(f"Step index {state.current_step_index} is out of range (0-{len(state.step_table_json)-1})")
                else:
                    st.error("No step table available")

                # Force state update in session state
                st.session_state['state'] = state
                st.rerun()

def _setup_page_style():
    """Set up the page style with custom CSS."""
    try:
        css_path = os.path.join(os.path.dirname(__file__), "style.css")
        with open(css_path) as f:
            st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.error(f"CSS file not found: {css_path}")
        # Fallback to inline CSS if file not found
        st.markdown("""
        <style>
            .main-header {
                font-size: 2.5rem;
                color: #673AB7;
                text-align: center;
                margin-bottom: 1rem;
            }
            .sub-header {
                font-size: 1.8rem;
                color: #8E24AA;
                margin-top: 2rem;
                margin-bottom: 1rem;
            }
            .stage-header, .step-header {
                font-size: 1.5rem;
                color: #5C6BC0;
                margin-top: 1.5rem;
                margin-bottom: 1rem;
                background-color: #F0E6FF;
                padding: 0.5rem;
                border-radius: 0.5rem;
            }
        </style>
        """, unsafe_allow_html=True)



def _display_workflow_summary_sidebar(state):
    """
    Display a streamlined summary of the current workflow state in the sidebar.

    Simplified for the new mode-based navigation system.

    Args:
        state (StateManager): The application state manager instance
    """
    # Get current mode information
    from core.mode_navigation import get_current_mode, ApplicationMode

    current_mode = get_current_mode(state)
    current_stage = state.current_stage

    # Display current status section
    st.markdown("### 📊 Current Status")

    # Show current mode and stage
    mode_names = {
        ApplicationMode.SCRIPT_GENERATION: "🔧 Script Generation",
        ApplicationMode.SCRIPT_VIEWER: "📜 Script Viewer",
        ApplicationMode.SCRIPT_PLAYGROUND: "🎮 Script Playground"
    }

    current_mode_name = mode_names.get(current_mode, "Unknown Mode")
    st.markdown(f"**Mode:** {current_mode_name}")
    st.markdown(f"**Stage:** {current_stage.get_display_name()}")

    st.markdown("---")

    # Current Status Section - Consolidated and streamlined
    with st.expander("Current Status", expanded=True):
        # Suite level information (Stage 1)
        if hasattr(state, 'test_cases') and state.test_cases:
            test_case_count = len(state.test_cases)
            st.success(f"✅ Test Suite: {test_case_count} test cases loaded")
        else:
            st.warning("⚠️ Test Suite: No test cases loaded")

        # Website URL information (Stage 2)
        if hasattr(state, 'website_url') and state.website_url and state.website_url != "https://example.com":
            st.success(f"✅ Website: {state.website_url}")
        else:
            st.warning("⚠️ Website: Not set")

        # Test case level information (Stage 3)
        if hasattr(state, 'selected_test_case') and state.selected_test_case:
            # Test case is selected
            tc_id = state.selected_test_case.get('Test Case ID', 'Unknown')
            tc_objective = state.selected_test_case.get('Test Case Objective', '')

            # Truncate objective if too long
            if len(tc_objective) > 50:
                tc_objective = tc_objective[:47] + "..."

            st.success(f"✅ Test Case: {tc_id}")

            # Show conversion status
            if hasattr(state, 'conversion_done') and state.conversion_done and hasattr(state, 'step_table_json') and state.step_table_json:
                # Stage 3 is completed
                step_count = len(state.step_table_json)
                st.success(f"✅ Conversion: {step_count} steps")
            else:
                # Stage 3 is not completed
                st.warning("⚠️ Conversion: Not completed")
        else:
            # No test case selected
            st.warning("⚠️ Test Case: None selected")

    # Step level information - Only show if a step is selected
    if hasattr(state, 'selected_step') and state.selected_step:
        with st.expander("Current Step Details", expanded=True):
            step_no = state.selected_step.get('Step No', 'Unknown')
            step_action = state.selected_step.get('Test Steps', '')

            # Truncate step action if too long
            if len(step_action) > 50:
                step_action = step_action[:47] + "..."

            # Create a more prominent step counter
            current_step = state.current_step_index + 1
            total_steps = state.total_steps

            # Display step counter with large text
            st.markdown(f"**Step {current_step} of {total_steps}**")
            st.markdown(f"**Step No:** {step_no}")

            if step_action:
                st.markdown(f"**Action:** {step_action}")

            # Show step status with concise visual indicators
            status_col1, status_col2 = st.columns(2)

            with status_col1:
                # UI Elements status
                if hasattr(state, 'step_matches') and state.step_matches:
                    st.success("✅ UI Elements")
                else:
                    st.warning("⚠️ UI Elements")

                # Script status
                if hasattr(state, 'generated_script_path') and state.generated_script_path:
                    st.success("✅ Script")
                else:
                    st.warning("⚠️ Script")

            with status_col2:
                # Test Data status
                if hasattr(state, 'test_data') and state.test_data:
                    st.success("✅ Test Data")
                elif hasattr(state, 'test_data_skipped') and state.test_data_skipped:
                    st.success("✅ Data Skipped")
                else:
                    st.warning("⚠️ Test Data")

                # Show step progress
                if hasattr(state, 'completed_steps') and isinstance(state.completed_steps, list):
                    completed_count = len(state.completed_steps)
                    completion_pct = int((completed_count / total_steps) * 100) if total_steps > 0 else 0
                    st.success(f"✅ Progress: {completion_pct}%")

def _setup_sidebar(state):
    """Set up the sidebar with workflow summary, branding, navigation, and usage stats."""
    with st.sidebar:
        # Add ScriptWeaver branding
        st.image("https://cogniron.com/wp-content/uploads/2024/10/image-69.png", width=300)

        # Display workflow summary
        _display_workflow_summary_sidebar(state)

        st.markdown("---")

        # Add mode-based navigation
        try:
            render_mode_navigation(state)

        except Exception as e:
            debug(f"Error rendering mode navigation: {e}",
                  stage="navigation", operation="mode_navigation_error",
                  context={'error_type': type(e).__name__, 'error_message': str(e)})
            st.error("⚠️ Navigation component error")

        st.markdown("---")
        st.markdown("### ☁️ Google AI Studio Usage")
        from datetime import timedelta
        now = datetime.now()
        one_minute_ago = now - timedelta(minutes=1)
        one_day_ago = now - timedelta(days=1)
        # Ensure state lists exist
        if not hasattr(state, 'google_request_timestamps'):
            state.google_request_timestamps = []
        if not hasattr(state, 'google_token_usage'):
            state.google_token_usage = []
        # Filter timestamps for the relevant periods
        requests_last_minute = [ts for ts in state.google_request_timestamps if ts > one_minute_ago]
        requests_last_day = [ts for ts in state.google_request_timestamps if ts > one_day_ago]
        # Filter token usage for the last minute
        tokens_last_minute_data = [tup for tup in state.google_token_usage if tup[0] > one_minute_ago]
        tokens_last_minute_sum = sum(count for _, count in tokens_last_minute_data)
        # Calculate metrics
        current_rpm = len(requests_last_minute)
        current_tpm = tokens_last_minute_sum
        current_rpd = len(requests_last_day)
        with st.expander("Google AI Studio Usage (Live)", expanded=False):
            st.metric("TPM (Tokens/Minute)", f"{current_tpm:,}")
            st.metric("RPM (Requests/Minute)", f"{current_rpm}")
            st.metric("RPD (Requests/Day)", f"{current_rpd}")
            if st.button("Clear Usage Stats", key="clear_google_usage_stats_sidebar"):
                state.google_request_timestamps = []
                state.google_token_usage = []
                st.success("Usage stats cleared")
                st.rerun()

# Stage functions are now imported from stages.py

def run_app():
    """Main function to run the Streamlit application."""

    # Clean up one-shot flags from previous run
    from utils.flag_helpers import cleanup_one_shot_flags
    cleanup_one_shot_flags()

    # --- Initialize State Manager ---
    from state_manager import StateManager
    StateManager().init_in_session(st)
    state = StateManager.get(st)

    # --- Page Setup ---
    # Page config is now set at the top of the file
    _setup_page_style()

    # Debug information about the state
    try:
        debug_mode = st.secrets.get("debug", False)
    except:
        debug_mode = False

    if debug_mode:
        print("=== State Manager Initialization ===")
        print(f"State: current_step_index = {state.current_step_index}")
        print(f"State: total_steps = {state.total_steps}")
        print(f"State: step_ready_for_script = {state.step_ready_for_script}")
        print(f"State: all_steps_done = {state.all_steps_done}")
        print("=== End State Manager Initialization ===")

    # Setup sidebar
    _setup_sidebar(state)

    # --- Header with ScriptWeaver branding ---
    st.markdown("<h1 class='main-header'>GretahAI ScriptWeaver</h1>", unsafe_allow_html=True)
    st.markdown("""
    <div class="feature-panel">
    Generate executable PyTest scripts from test cases.
    </div>
    """, unsafe_allow_html=True)

    # Create a container for stage progression messages
    stage_progression_container = st.container()

    # Check for stage progression messages in session state
    if 'stage_progression_message' in st.session_state:
        with stage_progression_container:
            st.success(st.session_state['stage_progression_message'])
            # Remove the message so it doesn't show up again
            del st.session_state['stage_progression_message']

    # ===== CENTRALIZED STAGE-BASED ROUTING =====

    # Import StateStage for routing logic
    from state_manager import StateStage

    # Route to the appropriate stage based on current_stage
    current_stage = state.current_stage

    # CRITICAL FIX: Ensure current_stage is always a proper StateStage enum
    if not isinstance(current_stage, StateStage):
        debug(f"Current stage is not a StateStage enum: {type(current_stage)} = {current_stage}",
              stage="stage_management", operation="stage_validation_warning",
              context={'current_stage_type': type(current_stage).__name__, 'current_stage_value': str(current_stage)})
        current_stage = StateStage.safe_from_any(current_stage)
        state.current_stage = current_stage
        debug(f"Corrected current_stage to: {current_stage.get_display_name()}",
              stage="stage_management", operation="stage_correction",
              context={'corrected_stage': current_stage.get_display_name()})

    debug(f"Routing to {current_stage.get_display_name()}",
          stage="navigation", operation="stage_routing",
          context={'target_stage': current_stage.get_display_name()})

    # Handle navigation state changes first
    try:
        from core.stage_navigation import handle_navigation_state_changes
        navigation_occurred = handle_navigation_state_changes(state)
        if navigation_occurred:
            current_stage = state.current_stage
            # Ensure stage is still valid after navigation
            if not isinstance(current_stage, StateStage):
                current_stage = StateStage.safe_from_any(current_stage)
                state.current_stage = current_stage
    except Exception as e:
        debug(f"Error handling navigation state changes: {e}",
              stage="navigation", operation="navigation_state_error",
              context={'error_type': type(e).__name__, 'error_message': str(e)})

    # Handle special session state transitions
    if 'transitioning_to_stage8' in st.session_state:
        state.advance_to(StateStage.STAGE8_OPTIMIZE, "Transitioning from Stage 7")
        del st.session_state['transitioning_to_stage8']
        current_stage = state.current_stage

    if 'transitioning_to_stage3' in st.session_state:
        state.advance_to(StateStage.STAGE3_CONVERT, "Transitioning from Stage 7")
        del st.session_state['transitioning_to_stage3']
        current_stage = state.current_stage

    if 'transitioning_from_stage8' in st.session_state:
        state.advance_to(StateStage.STAGE3_CONVERT, "Transitioning from Stage 8")
        del st.session_state['transitioning_from_stage8']
        current_stage = state.current_stage

    if 'coming_from_stage7' in st.session_state:
        state.advance_to(StateStage.STAGE4_DETECT, "Coming from Stage 7")
        del st.session_state['coming_from_stage7']
        current_stage = state.current_stage

    # Route to appropriate stage function based on current_stage
    if current_stage == StateStage.STAGE1_UPLOAD:
        stage1_upload_excel(state)

    elif current_stage == StateStage.STAGE2_WEBSITE:
        stage1_upload_excel(state)  # Always show Stage 1
        stage2_enter_website(state)

    elif current_stage == StateStage.STAGE3_CONVERT:
        stage1_upload_excel(state)  # Always show Stage 1
        stage2_enter_website(state)  # Always show Stage 2
        stage3_convert_test_case(state)

    elif current_stage == StateStage.STAGE4_DETECT:
        stage1_upload_excel(state)  # Always show Stage 1
        stage2_enter_website(state)  # Always show Stage 2
        stage3_convert_test_case(state)  # Always show Stage 3
        stage4_ui_detection_and_matching(state)

    elif current_stage == StateStage.STAGE5_DATA:
        stage1_upload_excel(state)  # Always show Stage 1
        stage2_enter_website(state)  # Always show Stage 2
        stage3_convert_test_case(state)  # Always show Stage 3
        stage4_ui_detection_and_matching(state)
        stage5_test_data(state)

    elif current_stage == StateStage.STAGE6_GENERATE:
        stage1_upload_excel(state)  # Always show Stage 1
        stage2_enter_website(state)  # Always show Stage 2
        stage3_convert_test_case(state)  # Always show Stage 3
        stage4_ui_detection_and_matching(state)
        stage5_test_data(state)
        stage6_generate_script(state)

    elif current_stage == StateStage.STAGE7_EXECUTE:
        stage1_upload_excel(state)  # Always show Stage 1
        stage2_enter_website(state)  # Always show Stage 2
        stage3_convert_test_case(state)  # Always show Stage 3
        stage4_ui_detection_and_matching(state)
        stage5_test_data(state)
        stage6_generate_script(state)
        stage7_run_script(state)

    elif current_stage == StateStage.STAGE8_OPTIMIZE:
        stage8_optimize_script(state)

    elif current_stage == StateStage.STAGE9_BROWSE:
        stage9_browse_scripts(state)

    elif current_stage == StateStage.STAGE10_PLAYGROUND:
        stage10_script_playground(state)

    else:
        # CRITICAL FIX: Improved fallback with safer stage recovery
        debug(f"Unknown stage: {current_stage}, attempting safe stage recovery",
              stage="stage_management", operation="stage_recovery_attempt",
              context={'unknown_stage': str(current_stage), 'stage_type': type(current_stage).__name__})

        # CRITICAL FIX: Use safer stage determination instead of update_stage_based_on_completion
        if hasattr(state, '_determine_stage_from_state'):
            try:
                recovered_stage = state._determine_stage_from_state(state)
                if recovered_stage != current_stage:
                    debug(f"Stage recovery successful: {current_stage} -> {recovered_stage.get_display_name()}",
                          stage="stage_management", operation="stage_recovery_success",
                          context={'original_stage': str(current_stage), 'recovered_stage': recovered_stage.get_display_name()})
                    state.current_stage = recovered_stage
                    # Restart routing with recovered stage
                    st.rerun()
                    return
                else:
                    debug("Stage recovery determined same stage - no change needed",
                          stage="stage_management", operation="stage_recovery_no_change",
                          context={'current_stage': str(current_stage)})
            except Exception as e:
                debug(f"Stage recovery failed with exception: {e}",
                      stage="stage_management", operation="stage_recovery_error",
                      context={'error_type': type(e).__name__, 'error_message': str(e), 'current_stage': str(current_stage)})

        # CRITICAL FIX: More conservative fallback - only go to Stage 1 if we have no progress
        has_any_progress = (
            bool(getattr(state, 'uploaded_excel', None)) or
            bool(getattr(state, 'uploaded_file', None)) or
            bool(getattr(state, 'test_cases', None)) or
            bool(getattr(state, 'website_url', None)) or
            bool(getattr(state, 'selected_test_case', None))
        )

        if has_any_progress:
            debug("CRITICAL FIX: Preserving progress - not reverting to Stage 1",
                  stage="stage_management", operation="progress_preservation",
                  context={'current_stage': str(current_stage), 'has_progress': True})
            debug(f"Keeping unknown stage: {current_stage}",
                  stage="stage_management", operation="stage_preservation",
                  context={'preserved_stage': str(current_stage)})
            st.warning(f"⚠️ Unknown application stage: {current_stage}. Please use navigation to continue.")
            # Show Stage 1 as a safe fallback but don't change the stage
            stage1_upload_excel(state)
        else:
            debug(f"No progress detected, defaulting to Stage 1 from unknown stage: {current_stage}",
                  stage="stage_management", operation="fallback_to_stage1",
                  context={'unknown_stage': str(current_stage), 'has_progress': False})
            st.error("⚠️ Application state issue detected. Returning to file upload stage.")

            # Use advance_to for proper transition logging
            success = state.advance_to(StateStage.STAGE1_UPLOAD, f"Unknown stage fallback from: {current_stage}")
            if success:
                stage1_upload_excel(state)
            else:
                # If even the fallback fails, show error
                st.error("❌ Critical application error. Please refresh the page.")
                debug("Failed to advance to Stage 1 fallback",
                      stage="error_handling", operation="critical_fallback_error",
                      context={'attempted_fallback': 'STAGE1_UPLOAD', 'success': False})

    # Log the current state for debugging
    print(f"End of run_app: step_index={state.current_step_index}, total_steps={state.total_steps}, ready_for_script={state.step_ready_for_script}")

    # Commercial Footer with comprehensive licensing information
    st.markdown("---")
    st.markdown("""
    <div class="scriptweaver-footer">
        <div style="margin-bottom: 10px;">
            <strong>GretahAI ScriptWeaver</strong> v2.1.0 | © 2025 Cogniron. All Rights Reserved.
        </div>
        <div style="font-size: 0.8rem; color: rgba(255,255,255,0.8); margin-bottom: 8px;">
            <strong>PROPRIETARY COMMERCIAL SOFTWARE</strong> - This software is proprietary and confidential.
        </div>
        <div style="font-size: 0.8rem; color: rgba(255,255,255,0.8); margin-bottom: 8px;">
            <strong>Commercial Licensing:</strong> Valid commercial license required for all use.
            Unauthorized copying, distribution, or use is strictly prohibited.
        </div>
        <div style="font-size: 0.8rem; color: rgba(255,255,255,0.8);">
            <strong>Enterprise Support:</strong> <a href="mailto:<EMAIL>" style="color: #B39DDB;"><EMAIL></a> |
            <strong>Website:</strong> <a href="https://cogniron.com" target="_blank" style="color: #B39DDB;">cogniron.com</a>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Add debug information at the bottom
    _show_debug_info(state)

    # CRITICAL FIX: Add stage transition monitoring
    try:
        from utils.stage_monitor import display_stage_debug_info
        display_stage_debug_info()
    except Exception as e:
        debug(f"Failed to display stage debug info: {e}",
              stage="error_handling", operation="debug_display_error",
              context={'error_type': type(e).__name__, 'error_message': str(e)})

if __name__ == "__main__":
    run_app()
