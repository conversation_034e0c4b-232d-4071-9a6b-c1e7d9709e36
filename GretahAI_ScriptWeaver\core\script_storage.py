"""
Persistent Script Storage for GretahAI ScriptWeaver

This module provides persistent storage for generated scripts across application sessions.
It uses SQLite database to store script content, metadata, and session information,
enabling the Script Browser (Stage 9) to function as an independent, always-accessible utility.

Key Features:
- SQLite database for reliable persistence
- Script versioning and metadata tracking
- Session-independent script retrieval
- Automatic cleanup of old scripts
- Thread-safe database operations

Database Schema:
- scripts: Main script storage with content and metadata
- sessions: Track application sessions for organization
- script_metadata: Extended metadata for scripts
"""

import os
import sqlite3
import json
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager
import uuid
from debug_utils import debug

# Thread-local storage for database connections
thread_local = threading.local()

# Database configuration
DB_TIMEOUT = 30.0  # seconds
MAX_SCRIPT_AGE_DAYS = 90  # Keep scripts for 90 days
CLEANUP_INTERVAL_HOURS = 24  # Run cleanup every 24 hours


class ScriptStorage:
    """
    Persistent storage manager for test scripts.

    Provides methods to save, retrieve, and manage test scripts across
    application sessions using SQLite database.
    """

    def __init__(self, db_path: str = None):
        """
        Initialize the script storage.

        Args:
            db_path: Path to SQLite database file. If None, uses default location.
        """
        if db_path is None:
            # Use default path in the application directory
            app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            db_path = os.path.join(app_dir, "script_storage.db")

        self.db_path = db_path
        self.session_id = str(uuid.uuid4())
        self._last_cleanup = None

        # Ensure database directory exists
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

        # Initialize database
        self._init_database()

        # Register current session
        self._register_session()

        debug(f"ScriptStorage initialized with database: {self.db_path}",
              stage="script_storage", operation="initialization",
              context={"db_path": self.db_path, "session_id": self.session_id})
        debug(f"Current session ID: {self.session_id}",
              stage="script_storage", operation="session_registration",
              context={"session_id": self.session_id})

    def _get_connection(self) -> sqlite3.Connection:
        """
        Get thread-local database connection.

        Returns:
            SQLite connection object
        """
        if not hasattr(thread_local, 'connection') or thread_local.connection is None:
            try:
                conn = sqlite3.connect(
                    self.db_path,
                    timeout=DB_TIMEOUT,
                    check_same_thread=False
                )
                conn.row_factory = sqlite3.Row
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute(f"PRAGMA busy_timeout={int(DB_TIMEOUT * 1000)}")
                conn.execute("PRAGMA foreign_keys=ON")
                thread_local.connection = conn
                debug(f"Created new database connection for thread {threading.get_ident()}",
                      stage="script_storage", operation="connection_created",
                      context={"thread_id": threading.get_ident(), "db_path": self.db_path})
            except sqlite3.Error as e:
                debug(f"Failed to create database connection: {e}",
                      stage="script_storage", operation="connection_error",
                      context={"error": str(e), "db_path": self.db_path})
                raise

        return thread_local.connection

    @contextmanager
    def _get_cursor(self):
        """
        Context manager for database cursor with automatic transaction handling.

        Yields:
            SQLite cursor object
        """
        conn = self._get_connection()
        cursor = conn.cursor()
        try:
            yield cursor
            conn.commit()
        except Exception as e:
            conn.rollback()
            debug(f"Database transaction failed: {e}",
                  stage="script_storage", operation="transaction_error",
                  context={"error": str(e), "error_type": type(e).__name__})
            raise
        finally:
            cursor.close()

    def _init_database(self):
        """Initialize database schema if it doesn't exist."""
        try:
            with self._get_cursor() as cursor:
                # Sessions table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sessions (
                        id TEXT PRIMARY KEY,
                        start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        end_time TIMESTAMP,
                        metadata TEXT
                    )
                ''')

                # Scripts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scripts (
                        id TEXT PRIMARY KEY,
                        session_id TEXT,
                        script_type TEXT NOT NULL,
                        test_case_id TEXT,
                        step_no TEXT,
                        content TEXT NOT NULL,
                        file_path TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (session_id) REFERENCES sessions (id)
                    )
                ''')

                # Script metadata table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS script_metadata (
                        script_id TEXT PRIMARY KEY,
                        metadata TEXT,
                        file_size INTEGER,
                        line_count INTEGER,
                        generation_timestamp TEXT,
                        validation_status TEXT,
                        optimization_status TEXT,
                        FOREIGN KEY (script_id) REFERENCES scripts (id) ON DELETE CASCADE
                    )
                ''')

                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_scripts_session ON scripts (session_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_scripts_type ON scripts (script_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_scripts_test_case ON scripts (test_case_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_scripts_created ON scripts (created_at)')

                debug("Database schema initialized successfully",
                      stage="script_storage", operation="schema_initialization",
                      context={"db_path": self.db_path})

        except sqlite3.Error as e:
            debug(f"Failed to initialize database schema: {e}",
                  stage="script_storage", operation="schema_error",
                  context={"error": str(e), "db_path": self.db_path})
            raise

    def _register_session(self):
        """Register the current application session."""
        try:
            with self._get_cursor() as cursor:
                cursor.execute(
                    'INSERT OR REPLACE INTO sessions (id, start_time) VALUES (?, ?)',
                    (self.session_id, datetime.now())
                )
                debug(f"Registered session: {self.session_id}",
                      stage="script_storage", operation="session_registered",
                      context={"session_id": self.session_id})
        except sqlite3.Error as e:
            debug(f"Failed to register session: {e}",
                  stage="script_storage", operation="session_registration_error",
                  context={"error": str(e), "session_id": self.session_id})
            raise

    def save_script(self, script_content: str, script_type: str, test_case_id: str = None,
                   step_no: str = None, file_path: str = None, metadata: Dict[str, Any] = None) -> str:
        """
        Save a script to persistent storage.

        Args:
            script_content: The script content to save
            script_type: Type of script ('step', 'combined', 'optimized')
            test_case_id: ID of the test case
            step_no: Step number (for step scripts)
            file_path: Path to the script file
            metadata: Additional metadata about the script

        Returns:
            str: Unique script ID
        """
        script_id = str(uuid.uuid4())

        try:
            with self._get_cursor() as cursor:
                # Ensure session exists before inserting script
                cursor.execute('SELECT id FROM sessions WHERE id = ?', (self.session_id,))
                if not cursor.fetchone():
                    # Re-register session if it doesn't exist
                    debug(f"Session {self.session_id} not found, re-registering...",
                          stage="script_storage", operation="session_reregistration",
                          context={"session_id": self.session_id})
                    self._register_session()

                # Insert script record
                cursor.execute('''
                    INSERT INTO scripts (id, session_id, script_type, test_case_id, step_no,
                                       content, file_path, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    script_id, self.session_id, script_type, test_case_id, step_no,
                    script_content, file_path, datetime.now(), datetime.now()
                ))

                # Insert metadata if provided
                if metadata:
                    metadata_json = json.dumps(metadata)
                    file_size = len(script_content)
                    line_count = len(script_content.splitlines())
                    generation_timestamp = metadata.get('generation_timestamp', datetime.now().isoformat())
                    validation_status = metadata.get('validation_status', 'pending')
                    optimization_status = metadata.get('optimization_status', 'optimized' if script_type == 'optimized' else 'none')

                    cursor.execute('''
                        INSERT INTO script_metadata (script_id, metadata, file_size, line_count,
                                                    generation_timestamp, validation_status, optimization_status)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        script_id, metadata_json, file_size, line_count,
                        generation_timestamp, validation_status, optimization_status
                    ))
                else:
                    # Insert basic metadata even if none provided
                    file_size = len(script_content)
                    line_count = len(script_content.splitlines())
                    generation_timestamp = datetime.now().isoformat()
                    validation_status = 'pending'
                    optimization_status = 'optimized' if script_type == 'optimized' else 'none'

                    cursor.execute('''
                        INSERT INTO script_metadata (script_id, metadata, file_size, line_count,
                                                    generation_timestamp, validation_status, optimization_status)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        script_id, '{}', file_size, line_count,
                        generation_timestamp, validation_status, optimization_status
                    ))

                debug(f"Saved script {script_id} (type: {script_type}, test_case: {test_case_id})",
                      stage="script_storage", operation="script_saved",
                      context={"script_id": script_id, "script_type": script_type,
                              "test_case_id": test_case_id, "step_no": step_no})
                return script_id

        except sqlite3.Error as e:
            debug(f"Failed to save script: {e}",
                  stage="script_storage", operation="script_save_error",
                  context={"error": str(e), "script_type": script_type, "test_case_id": test_case_id})
            raise

    def get_all_scripts(self, include_current_session: bool = True) -> List[Dict[str, Any]]:
        """
        Retrieve all scripts from storage.

        Args:
            include_current_session: Whether to include scripts from current session

        Returns:
            List of script dictionaries with metadata
        """
        try:
            with self._get_cursor() as cursor:
                query = '''
                    SELECT s.*, sm.metadata, sm.file_size, sm.line_count,
                           sm.generation_timestamp, sm.validation_status, sm.optimization_status
                    FROM scripts s
                    LEFT JOIN script_metadata sm ON s.id = sm.script_id
                '''

                if not include_current_session:
                    query += ' WHERE s.session_id != ?'
                    cursor.execute(query + ' ORDER BY s.created_at DESC', (self.session_id,))
                else:
                    cursor.execute(query + ' ORDER BY s.created_at DESC')

                scripts = []
                for row in cursor.fetchall():
                    script_dict = {
                        'id': row['id'],
                        'session_id': row['session_id'],
                        'type': row['script_type'],
                        'test_case_id': row['test_case_id'],
                        'step_no': row['step_no'],
                        'content': row['content'],
                        'file_path': row['file_path'],
                        'timestamp': datetime.fromisoformat(row['created_at']) if row['created_at'] else datetime.now(),
                        'updated_at': datetime.fromisoformat(row['updated_at']) if row['updated_at'] else datetime.now(),
                    }

                    # Add metadata if available
                    if row['metadata']:
                        try:
                            custom_metadata = json.loads(row['metadata'])
                            script_dict['metadata'] = custom_metadata
                        except json.JSONDecodeError:
                            script_dict['metadata'] = {}
                    else:
                        script_dict['metadata'] = {}

                    # Add computed metadata
                    script_dict['file_size'] = row['file_size'] or len(script_dict['content'])
                    script_dict['line_count'] = row['line_count'] or len(script_dict['content'].splitlines())
                    script_dict['validation_status'] = row['validation_status'] or 'unknown'
                    script_dict['optimization_status'] = row['optimization_status'] or 'none'

                    scripts.append(script_dict)

                debug(f"Retrieved {len(scripts)} scripts from storage",
                      stage="script_storage", operation="scripts_retrieved",
                      context={"script_count": len(scripts), "include_current_session": include_current_session})
                return scripts

        except sqlite3.Error as e:
            debug(f"Failed to retrieve scripts: {e}",
                  stage="script_storage", operation="scripts_retrieval_error",
                  context={"error": str(e), "include_current_session": include_current_session})
            return []

    def get_scripts_by_test_case(self, test_case_id: str) -> List[Dict[str, Any]]:
        """
        Retrieve all scripts for a specific test case.

        Args:
            test_case_id: ID of the test case

        Returns:
            List of script dictionaries
        """
        try:
            with self._get_cursor() as cursor:
                cursor.execute('''
                    SELECT s.*, sm.metadata, sm.file_size, sm.line_count,
                           sm.generation_timestamp, sm.validation_status, sm.optimization_status
                    FROM scripts s
                    LEFT JOIN script_metadata sm ON s.id = sm.script_id
                    WHERE s.test_case_id = ?
                    ORDER BY s.created_at DESC
                ''', (test_case_id,))

                scripts = []
                for row in cursor.fetchall():
                    script_dict = self._row_to_script_dict(row)
                    scripts.append(script_dict)

                debug(f"Retrieved {len(scripts)} scripts for test case {test_case_id}",
                      stage="script_storage", operation="test_case_scripts_retrieved",
                      context={"script_count": len(scripts), "test_case_id": test_case_id})
                return scripts

        except sqlite3.Error as e:
            debug(f"Failed to retrieve scripts for test case {test_case_id}: {e}",
                  stage="script_storage", operation="test_case_scripts_error",
                  context={"error": str(e), "test_case_id": test_case_id})
            return []

    def _row_to_script_dict(self, row) -> Dict[str, Any]:
        """
        Convert database row to script dictionary.

        Args:
            row: SQLite row object

        Returns:
            Script dictionary
        """
        # Safely extract values with None handling
        script_dict = {
            'id': row['id'] or str(uuid.uuid4()),
            'session_id': row['session_id'],
            'type': row['script_type'] or 'unknown',
            'test_case_id': row['test_case_id'],  # Can be None
            'step_no': row['step_no'],  # Can be None
            'content': row['content'] or '',
            'file_path': row['file_path'],  # Can be None
            'timestamp': datetime.fromisoformat(row['created_at']) if row['created_at'] else datetime.now(),
            'updated_at': datetime.fromisoformat(row['updated_at']) if row['updated_at'] else datetime.now(),
        }

        # Add metadata if available
        if row['metadata']:
            try:
                custom_metadata = json.loads(row['metadata'])
                script_dict['metadata'] = custom_metadata
            except json.JSONDecodeError:
                debug(f"Failed to parse metadata for script {script_dict['id']}",
                      stage="script_storage", operation="metadata_parse_error",
                      context={"script_id": script_dict['id']})
                script_dict['metadata'] = {}
        else:
            script_dict['metadata'] = {}

        # Add computed metadata with safe calculations
        content = script_dict['content'] or ''
        script_dict['file_size'] = row['file_size'] or len(content)
        script_dict['line_count'] = row['line_count'] or len(content.splitlines())
        script_dict['validation_status'] = row['validation_status'] or 'unknown'
        script_dict['optimization_status'] = row['optimization_status'] or 'none'

        return script_dict

    def get_script_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about stored scripts.

        Returns:
            Dictionary containing script statistics
        """
        try:
            with self._get_cursor() as cursor:
                # Total scripts
                cursor.execute('SELECT COUNT(*) as total FROM scripts')
                total_scripts = cursor.fetchone()['total']

                # Scripts by type
                cursor.execute('''
                    SELECT script_type, COUNT(*) as count
                    FROM scripts
                    GROUP BY script_type
                ''')
                script_types = {row['script_type']: row['count'] for row in cursor.fetchall()}

                # Scripts by test case
                cursor.execute('''
                    SELECT test_case_id, COUNT(*) as count
                    FROM scripts
                    WHERE test_case_id IS NOT NULL
                    GROUP BY test_case_id
                ''')
                test_cases = {row['test_case_id']: row['count'] for row in cursor.fetchall()}

                # Date range
                cursor.execute('''
                    SELECT MIN(created_at) as earliest, MAX(created_at) as latest
                    FROM scripts
                ''')
                date_row = cursor.fetchone()
                date_range = None
                if date_row['earliest'] and date_row['latest']:
                    date_range = (
                        datetime.fromisoformat(date_row['earliest']),
                        datetime.fromisoformat(date_row['latest'])
                    )

                # Total size and lines
                cursor.execute('''
                    SELECT SUM(file_size) as total_size, SUM(line_count) as total_lines
                    FROM script_metadata
                ''')
                size_row = cursor.fetchone()

                return {
                    'total_scripts': total_scripts,
                    'script_types': script_types,
                    'test_cases': test_cases,
                    'date_range': date_range,
                    'total_size': size_row['total_size'] or 0,
                    'total_lines': size_row['total_lines'] or 0
                }

        except sqlite3.Error as e:
            debug(f"Failed to get script statistics: {e}",
                  stage="script_storage", operation="statistics_error",
                  context={"error": str(e)})
            return {
                'total_scripts': 0,
                'script_types': {},
                'test_cases': {},
                'date_range': None,
                'total_size': 0,
                'total_lines': 0
            }

    def cleanup_old_scripts(self, max_age_days: int = MAX_SCRIPT_AGE_DAYS) -> int:
        """
        Clean up old scripts from storage.

        Args:
            max_age_days: Maximum age of scripts to keep in days

        Returns:
            Number of scripts deleted
        """
        cutoff_date = datetime.now() - timedelta(days=max_age_days)

        try:
            with self._get_cursor() as cursor:
                # Delete old scripts (metadata will be deleted by CASCADE)
                cursor.execute(
                    'DELETE FROM scripts WHERE created_at < ?',
                    (cutoff_date,)
                )
                deleted_count = cursor.rowcount

                # Clean up orphaned sessions
                cursor.execute('''
                    DELETE FROM sessions
                    WHERE id NOT IN (SELECT DISTINCT session_id FROM scripts WHERE session_id IS NOT NULL)
                ''')

                debug(f"Cleaned up {deleted_count} old scripts (older than {max_age_days} days)",
                      stage="script_storage", operation="cleanup_completed",
                      context={"deleted_count": deleted_count, "max_age_days": max_age_days})
                return deleted_count

        except sqlite3.Error as e:
            debug(f"Failed to cleanup old scripts: {e}",
                  stage="script_storage", operation="cleanup_error",
                  context={"error": str(e), "max_age_days": max_age_days})
            return 0

    def clear_all_scripts(self) -> int:
        """
        Clear ALL scripts from storage permanently.

        This method deletes all scripts, metadata, and sessions from the database.
        This action cannot be undone.

        Returns:
            Number of scripts deleted
        """
        try:
            with self._get_cursor() as cursor:
                # Count scripts before deletion for logging
                cursor.execute('SELECT COUNT(*) FROM scripts')
                script_count = cursor.fetchone()[0]

                # Delete all script metadata first (due to foreign key constraints)
                cursor.execute('DELETE FROM script_metadata')

                # Delete all scripts
                cursor.execute('DELETE FROM scripts')

                # Delete all sessions
                cursor.execute('DELETE FROM sessions')

                debug(f"CLEARED ALL SCRIPT HISTORY: Deleted {script_count} scripts and all associated data",
                      stage="script_storage", operation="clear_all_completed",
                      context={"deleted_count": script_count})
                return script_count

        except sqlite3.Error as e:
            debug(f"Failed to clear all scripts: {e}",
                  stage="script_storage", operation="clear_all_error",
                  context={"error": str(e)})
            raise

    def close(self):
        """Close database connections and cleanup."""
        try:
            # End current session
            with self._get_cursor() as cursor:
                cursor.execute(
                    'UPDATE sessions SET end_time = ? WHERE id = ?',
                    (datetime.now(), self.session_id)
                )

            # Close thread-local connection if exists
            if hasattr(thread_local, 'connection') and thread_local.connection:
                thread_local.connection.close()
                thread_local.connection = None

            debug(f"Closed script storage for session {self.session_id}",
                  stage="script_storage", operation="storage_closed",
                  context={"session_id": self.session_id})

        except sqlite3.Error as e:
            debug(f"Error closing script storage: {e}",
                  stage="script_storage", operation="close_error",
                  context={"error": str(e), "session_id": self.session_id})


# Global instance for easy access
_storage_instance = None


def get_script_storage() -> ScriptStorage:
    """
    Get the global script storage instance.

    Returns:
        ScriptStorage instance
    """
    global _storage_instance
    if _storage_instance is None:
        _storage_instance = ScriptStorage()
    return _storage_instance


def close_script_storage():
    """Close the global script storage instance."""
    global _storage_instance
    if _storage_instance:
        _storage_instance.close()
        _storage_instance = None
