"""
Stage 10: Script Template Manager for GretahAI ScriptWeaver

This module provides the core business logic for Stage 10 (Script Template Manager).
It focuses on StateManager integration, template loading, AI orchestration, and script generation
while delegating all UI rendering to the ui_components.stage10_components module.

Core Responsibilities:
- StateManager integration and session state management
- Template loading and validation workflows
- AI gap analysis and script generation orchestration
- Integration with Google AI API through core/ai.py
- Script execution coordination with proper error handling
- Comprehensive logging controlled by SCRIPTWEAVER_DEBUG

The refactored architecture follows established GretahAI patterns:
- UI logic extracted to ui_components.stage10_components
- Core business logic focused on StateManager and orchestration
- Professional enterprise styling maintained through UI components
- Independent API key management (hidden from UI)
- Centralized error handling and logging

Functions:
    stage10_script_playground(state): Main Stage 10 orchestration function

Phase 3d Enhancement: Standardized logging with centralized infrastructure
"""

import os
import logging
import streamlit as st
import tempfile
import subprocess
import shutil
import time
from datetime import datetime
from pathlib import Path

# Import core dependencies
from state_manager import StateStage
from core.ai import generate_llm_response
from core.ai_helpers import clean_llm_response
from debug_utils import debug

# Import helper functions
from core.template_helpers import (
    get_optimized_scripts_for_templates,
    format_template_script_display,
    get_available_test_cases,
    format_test_case_display,
    create_template_generation_filename,
    extract_template_structure_info
)

from core.template_prompt_builder import (
    generate_template_based_script_prompt,
    enhance_template_prompt_with_context
)

# Import UI components
from ui_components.stage10_components import (
    render_empty_playground_message,
    render_no_test_cases_message,
    render_template_selection_interface,
    render_test_case_selection_interface,
    render_gap_analysis_interface,
    render_gap_filling_form,
    render_script_generation_controls,
    render_script_execution_section_header,
    render_script_info_card,
    render_execution_controls_header,
    # render_execution_options_card,
    render_verbose_mode_checkbox,
    render_execution_status_indicator,
    render_execution_action_buttons,
    render_execution_results_header,
    render_execution_results_summary,
    render_execution_metrics_header,
    render_junit_metrics_grid,
    render_execution_output_section,
    render_execution_artifacts_section,
    render_stage10_footer,
    render_workflow_navigation,
    render_generation_success_display,
    render_failure_analysis_button,
    render_failure_analysis_results,
    render_regeneration_options
)

# Enhanced logging configuration using centralized infrastructure
from core.logging_config import get_stage_logger
logger = get_stage_logger("stage10")


def stage10_script_playground(state):
    """
    Stage 10: Script Template Manager.

    This function orchestrates the Stage 10 workflow by coordinating between UI components
    and core business logic. It focuses on StateManager integration, template loading,
    AI orchestration, and script generation while delegating UI rendering to components.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>🎮 Script Playground</h2>", unsafe_allow_html=True)
    st.markdown("*Experiment with script generation using optimized scripts as templates*")

    debug("Stage 10: Script Playground accessed",
          stage="stage10",
          operation="playground_initialization")

    # Initialize script storage if needed
    if not hasattr(state, '_script_storage') or state._script_storage is None:
        state._init_script_storage()

    # Load optimized scripts for templates and available test cases
    optimized_scripts = get_optimized_scripts_for_templates(state._script_storage)
    available_test_cases = get_available_test_cases(state)

    # Handle empty playground state
    if not optimized_scripts:
        col1, col2, col3 = render_empty_playground_message()
        
        with col1:
            if st.button(
                "📁 Start New Project",
                use_container_width=True,
                type="primary",
                key="stage10_empty_new_project"
            ):
                state.advance_to(StateStage.STAGE1_UPLOAD, "User started new project from Script Playground")
                st.rerun()
                return
        with col2:
            if st.button(
                "⚙️ Generate Scripts",
                use_container_width=True,
                key="stage10_empty_generate"
            ):
                state.advance_to(StateStage.STAGE6_GENERATE, "User navigated to script generation from Script Playground")
                st.rerun()
                return
        with col3:
            if st.button(
                "🔧 Optimize Scripts",
                use_container_width=True,
                key="stage10_empty_optimize"
            ):
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to optimization from Script Playground")
                st.rerun()
                return
        return

    # Handle no test cases available
    if not available_test_cases:
        render_no_test_cases_message()
        
        if st.button(
            "📁 Upload Test Cases",
            use_container_width=True,
            type="primary",
            key="stage10_no_cases_upload"
        ):
            state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to upload from Script Playground")
            st.rerun()
            return
        return

    # Main workflow: template selection, test case selection, and generation
    _handle_main_workflow(state, optimized_scripts, available_test_cases)


def _handle_main_workflow(state, optimized_scripts, available_test_cases):
    """
    Handle the main Stage 10 workflow using UI components.
    
    Args:
        state: StateManager instance
        optimized_scripts: List of available optimized scripts
        available_test_cases: List of available test cases
    """
    # Create template and test case mappings
    template_map = {}
    for script in optimized_scripts:
        display_info = format_template_script_display(script)
        option_text = f"{display_info['title']} - {display_info['timestamp']}"
        template_map[option_text] = script

    test_case_map = {}
    for test_case in available_test_cases:
        option_text = format_test_case_display(test_case)
        test_case_map[option_text] = test_case

    # Render template selection interface
    selected_template, _ = render_template_selection_interface(optimized_scripts, template_map)

    if not selected_template:
        return

    # Render test case selection interface
    selected_test_case = render_test_case_selection_interface(available_test_cases, test_case_map)

    if not selected_test_case:
        return

    # Render gap analysis interface
    gap_analysis_data = render_gap_analysis_interface(selected_template, selected_test_case)

    # Render enhanced gap handling options if gaps were identified
    gap_handling_option = None
    gap_responses = None
    if gap_analysis_data and gap_analysis_data.get('gaps_identified'):
        # Import the new gap handling options function
        from ui_components.stage10_components import render_gap_handling_options
        gap_handling_option, gap_responses = render_gap_handling_options(gap_analysis_data)

    # Render script generation controls with enhanced gap analysis integration
    generation_result = render_script_generation_controls(
        selected_template, selected_test_case, gap_analysis_data, gap_responses, gap_handling_option
    )

    if generation_result and len(generation_result) == 5:
        custom_instructions, preserve_structure, include_error_handling, generate_clicked, enhanced_context = generation_result

        if generate_clicked:
            _generate_script_from_template(
                state, selected_template, selected_test_case,
                custom_instructions, preserve_structure, include_error_handling,
                enhanced_context
            )

    # Display script generation results section (outside expander)
    _display_script_generation_results_if_available()

    # Handle script execution section
    _display_script_execution_section_if_available(state)

    # Render footer and navigation
    render_stage10_footer()
    
    # Handle workflow navigation
    stage1_clicked, stage8_clicked, stage9_clicked = render_workflow_navigation()
    
    if stage1_clicked:
        debug("User navigated to Stage 1 from Script Playground", stage="stage10", operation="navigation")
        state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to Stage 1 from Script Playground")
        st.rerun()
    elif stage8_clicked:
        debug("User navigated to Stage 8 from Script Playground", stage="stage10", operation="navigation")
        state.advance_to(StateStage.STAGE8_OPTIMIZE, "User navigated to Stage 8 from Script Playground")
        st.rerun()
    elif stage9_clicked:
        debug("User navigated to Stage 9 from Script Playground", stage="stage10", operation="navigation")
        state.advance_to(StateStage.STAGE9_BROWSE, "User navigated to Stage 9 from Script Playground")
        st.rerun()


def _generate_script_from_template(state, template_script, target_test_case,
                                 custom_instructions, preserve_structure, include_error_handling,
                                 enhanced_context=None):
    """
    Generate a new script using the selected template and test case with gap analysis integration.

    Args:
        state: StateManager instance
        template_script: Selected template script
        target_test_case: Target test case for generation
        custom_instructions: User's custom instructions
        preserve_structure: Whether to preserve template structure
        include_error_handling: Whether to include error handling
        enhanced_context: Enhanced context from gap analysis (optional)
    """
    try:
        debug("Starting template-based script generation",
              stage="stage10",
              operation="template_script_generation",
              context={
                  'template_id': template_script.get('id'),
                  'target_test_case': target_test_case.get('Test Case ID'),
                  'preserve_structure': preserve_structure,
                  'include_error_handling': include_error_handling
              })

        with st.spinner("🤖 Generating script from template..."):
            # Extract template structure information
            template_structure_info = extract_template_structure_info(template_script.get('content', ''))

            # Generate the prompt
            base_prompt = generate_template_based_script_prompt(
                template_script=template_script,
                target_test_case=target_test_case,
                template_structure_info=template_structure_info,
                website_url=getattr(state, 'website_url', None)
            )

            # Enhance prompt with additional context including gap analysis
            additional_context = {
                'custom_instructions': custom_instructions if custom_instructions else None,
                'preserve_structure': preserve_structure,
                'include_error_handling': include_error_handling
            }

            # Add gap analysis context if available
            if enhanced_context:
                gap_analysis_data = enhanced_context.get('gap_analysis')
                gap_responses = enhanced_context.get('gap_responses')
                if gap_analysis_data and gap_responses:
                    additional_context['gap_analysis'] = gap_analysis_data
                    additional_context['gap_responses'] = gap_responses
                    debug("Enhanced context with gap analysis data", stage="stage10", operation="context_enhancement")

            enhanced_prompt = enhance_template_prompt_with_context(base_prompt, additional_context)

            # Generate script using Google AI
            debug("Calling Google AI for template-based script generation",
                  stage="stage10",
                  operation="ai_script_generation",
                  context={
                      'model': "gemini-2.0-flash",
                      'category': "template_script_generation",
                      'prompt_length': len(enhanced_prompt)
                  })
            generated_script = generate_llm_response(
                prompt=enhanced_prompt,
                model_name="gemini-2.0-flash",
                api_key=getattr(state, 'google_api_key', None),
                category="template_script_generation",
                context={
                    'template_test_case_id': template_script.get('test_case_id', 'unknown'),
                    'target_test_case_id': target_test_case.get('Test Case ID', 'unknown'),
                    'template_script_id': template_script.get('id', 'unknown'),
                    'generation_type': 'template_based'
                }
            )

            if generated_script and generated_script.strip():
                _handle_successful_generation(state, template_script, target_test_case, generated_script)
            else:
                st.error("❌ Failed to generate script. Please try again.")
                debug("Template-based script generation failed - empty response",
                      stage="stage10", operation="error_handling",
                      context={'error_type': 'empty_response'})

    except Exception as e:
        error_msg = f"Template-based script generation failed: {e}"
        st.error(f"❌ **Generation Error**: {error_msg}")
        debug("Template-based script generation error",
              stage="stage10", operation="error_handling",
              context={'error': str(e), 'error_type': type(e).__name__})


def _handle_successful_generation(state, template_script, target_test_case, generated_script):
    """
    Handle successful script generation with display, storage, and execution using UI components.

    Args:
        state: StateManager instance
        template_script: Template script used
        target_test_case: Target test case
        generated_script: Generated script content
    """
    try:
        debug("Handling successful template-based script generation",
              stage="stage10", operation="generation_success")

        # Parse the generated script to extract clean Python code from markdown
        debug("Processing raw generated script", stage="stage10", operation="script_parsing",
              context={'raw_length': len(generated_script)})
        parsed_script = clean_llm_response(generated_script, "python")
        debug("Script parsing completed", stage="stage10", operation="script_parsing",
              context={'parsed_length': len(parsed_script)})

        # Create filename
        filename = create_template_generation_filename(template_script, target_test_case)

        # Store generation results in session state for display outside expander
        st.session_state['stage10_generation_results'] = {
            'parsed_script': parsed_script,
            'filename': filename,
            'target_test_case': target_test_case,
            'template_script': template_script,
            'generation_timestamp': datetime.now().isoformat(),
            'raw_generated_content': generated_script
        }

        # Save to script storage
        template_metadata = {
            'generation_type': 'template_based',
            'template_script_id': template_script.get('id'),
            'template_test_case_id': template_script.get('test_case_id'),
            'target_test_case_id': target_test_case.get('Test Case ID'),
            'generation_timestamp': datetime.now().isoformat(),
            'template_based': True,
            'optimization_status': 'template_generated'
        }

        state.add_script_to_history(
            script_content=parsed_script,
            script_type='template_generated',
            step_no=None,
            file_path=filename,
            metadata=template_metadata
        )

        debug("Template-based script saved", stage="stage10", operation="script_storage",
              context={'filename': filename})

        # Store generation data in session state for execution section (using parsed script)
        st.session_state['stage10_generated_script'] = {
            'script_content': parsed_script,
            'filename': filename,
            'target_test_case': target_test_case,
            'template_script': template_script,
            'generation_timestamp': datetime.now().isoformat(),
            'raw_generated_content': generated_script  # Keep original for debugging if needed
        }

        debug("Stored generated script data in session state for execution", stage="stage10", operation="session_state_update",
              context={'filename': filename})

    except Exception as e:
        error_msg = f"Failed to handle successful generation: {e}"
        st.error(f"❌ **Storage Error**: {error_msg}")
        debug("Error handling successful generation", stage="stage10", operation="error_handling",
              context={'error': str(e), 'error_type': type(e).__name__})


def _display_script_generation_results_if_available():
    """
    Display script generation results section outside of any expander.

    This function renders the generated script content, download buttons, and related
    interactive elements in an independent section to avoid Streamlit hierarchical
    click event issues with nested interactive elements.
    """
    try:
        # Check if we have generation results in session state
        if 'stage10_generation_results' not in st.session_state:
            return

        generation_data = st.session_state['stage10_generation_results']
        parsed_script = generation_data.get('parsed_script')
        filename = generation_data.get('filename')
        target_test_case = generation_data.get('target_test_case')
        template_script = generation_data.get('template_script')

        if not all([parsed_script, filename, target_test_case, template_script]):
            debug("Incomplete generation data in session state, skipping results display",
                  stage="stage10", operation="validation",
                  context={'missing_data': True})
            return

        debug("Displaying generation results for script", stage="stage10", operation="ui_display",
              context={'filename': filename})

        # Render section separator and header
        st.markdown("---")
        st.markdown("""
        <div style="text-align: center; margin: 2rem 0;">
            <h2 style="color: var(--primary-color); font-weight: 700; margin-bottom: 0.5rem;">
                📄 Generated Script
            </h2>
        </div>
        """, unsafe_allow_html=True)

        # Use the existing UI component to render the generation success display
        render_generation_success_display(parsed_script, filename, target_test_case, template_script)

        # Add clear button for generation results
        st.markdown("---")
        col1, col2, col3 = st.columns([1, 1, 1])
        with col2:
            if st.button(
                "🗑️ Clear Generated Script",
                use_container_width=True,
                type="secondary",
                key="stage10_clear_generated_script"
            ):
                # Clear generation results from session state
                if 'stage10_generation_results' in st.session_state:
                    del st.session_state['stage10_generation_results']
                # Also clear execution data if it exists
                if 'stage10_generated_script' in st.session_state:
                    del st.session_state['stage10_generated_script']
                # Clear any execution results
                execution_key = f"stage10_execution_results_{filename}"
                if execution_key in st.session_state:
                    del st.session_state[execution_key]
                st.success("✅ Generated script cleared successfully!")
                st.rerun()

        debug("Successfully displayed generation results", stage="stage10", operation="ui_display",
              context={'filename': filename})

    except Exception as e:
        error_msg = f"Failed to display script generation results: {e}"
        st.error(f"❌ **Display Error**: {error_msg}")
        debug("Error displaying script generation results", stage="stage10", operation="error_handling",
              context={'error': str(e), 'error_type': type(e).__name__})


def _display_script_execution_section_if_available(state):
    """
    Display script execution section using UI components.

    This function coordinates the script execution interface using the refactored
    UI components while maintaining the core business logic.

    Args:
        state: StateManager instance
    """
    try:
        # Check if we have generation results first, then sync to execution data
        if 'stage10_generation_results' in st.session_state:
            generation_data = st.session_state['stage10_generation_results']

            # Sync generation results to execution data if not already present
            if 'stage10_generated_script' not in st.session_state:
                st.session_state['stage10_generated_script'] = {
                    'script_content': generation_data.get('parsed_script'),
                    'filename': generation_data.get('filename'),
                    'target_test_case': generation_data.get('target_test_case'),
                    'template_script': generation_data.get('template_script'),
                    'generation_timestamp': generation_data.get('generation_timestamp'),
                    'raw_generated_content': generation_data.get('raw_generated_content')
                }

        # Check if we have a generated script in session state
        if 'stage10_generated_script' not in st.session_state:
            return

        script_data = st.session_state['stage10_generated_script']
        generated_script = script_data.get('script_content')
        filename = script_data.get('filename')
        target_test_case = script_data.get('target_test_case')

        if not all([generated_script, filename, target_test_case]):
            debug("Incomplete script data in session state, skipping execution section",
                  stage="stage10", operation="validation",
                  context={'missing_data': True})
            return

        debug("Displaying execution section for generated script", stage="stage10", operation="ui_display",
              context={'filename': filename})

        # Render professional section header
        render_script_execution_section_header()

        # Check execution status for status indicators
        execution_key = f"stage10_execution_results_{filename}"
        has_test_results = execution_key in st.session_state
        execution_status = "ready"

        if has_test_results:
            test_results = st.session_state[execution_key]
            execution_status = "passed" if test_results.get('success', False) else "failed"

        # Render script information card
        render_script_info_card(script_data, target_test_case, execution_status)

        # Render execution controls
        _render_execution_controls_with_ui_components(state, script_data, execution_status, filename)

        # Display execution results if available
        if has_test_results:
            test_results = st.session_state[execution_key]
            _display_execution_results_with_ui_components(test_results, target_test_case)

    except Exception as e:
        error_msg = f"Failed to display script execution section: {e}"
        st.error(f"❌ **Execution Display Error**: {error_msg}")
        debug("Error displaying script execution section", stage="stage10", operation="error_handling",
              context={'error': str(e), 'error_type': type(e).__name__})


def _render_execution_controls_with_ui_components(state, script_data, execution_status, filename):
    """
    Render execution controls using UI components.

    Args:
        state: StateManager instance
        script_data: Script data from session state
        execution_status: Current execution status
        filename: Script filename
    """
    generated_script = script_data.get('script_content')
    target_test_case = script_data.get('target_test_case')
    verbose_mode_key = f"stage10_verbose_{filename}"
    execution_key = f"stage10_execution_results_{filename}"

    # Render execution controls header
    render_execution_controls_header()

    # Render execution options card - removed as not needed
    # render_execution_options_card()

    # Render verbose mode checkbox
    verbose_mode = render_verbose_mode_checkbox(verbose_mode_key)

    # Render execution status indicator
    render_execution_status_indicator(execution_status)

    # Render execution action buttons and handle click states
    execute_clicked, clear_clicked = render_execution_action_buttons(execution_status, filename)

    # Handle button clicks immediately (following Stage 7 pattern)
    if execute_clicked:
        debug("Execute button clicked", stage="stage10", operation="user_interaction",
              context={'filename': filename, 'action': 'execute'})
        _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode)
        st.rerun()
        return

    if clear_clicked:
        debug("Clear button clicked", stage="stage10", operation="user_interaction",
              context={'filename': filename, 'action': 'clear'})
        # Clear the generated script from session state
        if 'stage10_generated_script' in st.session_state:
            del st.session_state['stage10_generated_script']
        # Clear execution results
        if execution_key in st.session_state:
            del st.session_state[execution_key]
        st.success("✅ Script cleared successfully!")
        st.rerun()
        return


def _display_execution_results_with_ui_components(test_results, target_test_case):
    """
    Display execution results using UI components.

    Args:
        test_results: Test execution results dictionary
        target_test_case: Target test case information
    """
    # Render execution results header
    render_execution_results_header()

    # Render execution results summary
    render_execution_results_summary(test_results, target_test_case)

    # Render execution metrics
    render_execution_metrics_header()

    # Parse and display JUnit XML results if available
    xml_results = test_results.get('xml_results')
    if xml_results:
        render_junit_metrics_grid(xml_results)

    # Render execution output section
    verbose_mode = st.session_state.get(f"stage10_verbose_{test_results.get('script_path', '')}", False)
    render_execution_output_section(test_results, verbose_mode)

    # Render execution artifacts section (screenshots, etc.)
    render_execution_artifacts_section(test_results, verbose_mode)

    # Render failure analysis button if execution failed
    filename = test_results.get('script_path', '').split('/')[-1] if test_results.get('script_path') else 'unknown'
    analyze_clicked = render_failure_analysis_button(test_results, filename)

    if analyze_clicked:
        _handle_failure_analysis(test_results, target_test_case, filename)


def _execute_generated_script(state, generated_script, filename, target_test_case, verbose_mode=False):
    """
    Execute the generated script using the same infrastructure as Stage 7/8.

    Args:
        state: StateManager instance
        generated_script: Generated script content
        filename: Script filename
        target_test_case: Target test case information
        verbose_mode: Whether to use verbose execution mode
    """
    try:
        debug("Executing generated script",
              stage="stage10",
              operation="script_execution",
              context={
                  'filename': filename,
                  'target_test_case': target_test_case.get('Test Case ID'),
                  'verbose_mode': verbose_mode
              })

        # Create a temporary file for the script
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as temp_file:
            temp_file.write(generated_script)
            temp_script_path = temp_file.name

        debug("Created temporary script file", stage="stage10", operation="file_creation",
              context={'temp_script_path': temp_script_path})

        with st.spinner(f"🧪 Executing generated script for {target_test_case.get('Test Case ID', 'Unknown')}..."):
            try:
                # Execute the script using enhanced Stage 10 execution method
                test_results = _execute_script_with_conftest(
                    script_path=temp_script_path,
                    filename=filename,
                    target_test_case=target_test_case,
                    verbose_mode=verbose_mode
                )

                # Store results in session state for persistence
                execution_key = f"stage10_execution_results_{filename}"
                st.session_state[execution_key] = test_results

                debug("Script execution completed and results stored", stage="stage10", operation="script_execution",
                      context={'filename': filename})

                # Show success/failure message
                if test_results.get('success', False):
                    st.success(f"✅ **Script executed successfully!** Test case {target_test_case.get('Test Case ID', 'Unknown')} passed.")
                else:
                    st.error(f"❌ **Script execution failed.** Check the results below for details.")

                # Trigger rerun to display results
                st.rerun()

            finally:
                # Clean up temporary file
                try:
                    if os.path.exists(temp_script_path):
                        os.unlink(temp_script_path)
                        debug("Cleaned up temporary script file", stage="stage10", operation="file_cleanup",
                              context={'temp_script_path': temp_script_path})
                except Exception as cleanup_error:
                    debug("Failed to clean up temporary script file", stage="stage10", operation="file_cleanup",
                          context={'error': str(cleanup_error), 'temp_script_path': temp_script_path})

    except Exception as e:
        error_msg = f"Failed to execute generated script: {e}"
        st.error(f"❌ **Execution Error**: {error_msg}")
        debug("Script execution error", stage="stage10", operation="error_handling",
              context={'error': str(e), 'error_type': type(e).__name__})


def _execute_script_with_conftest(script_path, filename, target_test_case, verbose_mode=False):
    """
    Execute a generated script with proper conftest.py integration and pytest configuration.

    This function ensures that:
    1. The script runs from the correct working directory (GretahAI_ScriptWeaver)
    2. The conftest.py file is accessible for browser fixture
    3. Required pytest plugins are handled gracefully
    4. Proper error handling for missing dependencies
    5. Temporary script is copied to project directory to ensure proper rootdir detection

    Args:
        script_path: Path to the temporary script file
        filename: Original filename for logging
        target_test_case: Target test case information
        verbose_mode: Whether to use verbose execution mode

    Returns:
        dict: Test execution results
    """
    try:
        debug("Executing script with conftest integration", stage="stage10", operation="script_execution",
              context={'filename': filename})

        # Determine the correct working directory (GretahAI_ScriptWeaver root)
        current_dir = os.getcwd()
        scriptweaver_dir = None

        # Check if we're already in GretahAI_ScriptWeaver directory
        if "GretahAI_ScriptWeaver" in current_dir:
            if current_dir.endswith("GretahAI_ScriptWeaver"):
                scriptweaver_dir = current_dir
            else:
                # Navigate to GretahAI_ScriptWeaver directory
                parts = current_dir.split(os.sep)
                try:
                    scriptweaver_index = parts.index("GretahAI_ScriptWeaver")
                    scriptweaver_dir = os.sep.join(parts[:scriptweaver_index + 1])
                except ValueError:
                    scriptweaver_dir = current_dir
        else:
            # Fallback to current directory
            scriptweaver_dir = current_dir

        debug("Using working directory", stage="stage10", operation="directory_setup",
              context={'working_directory': scriptweaver_dir})

        # Verify conftest.py exists
        conftest_path = os.path.join(scriptweaver_dir, "conftest.py")
        if not os.path.exists(conftest_path):
            debug("Warning: conftest.py not found", stage="stage10", operation="conftest_validation",
                  context={'conftest_path': conftest_path})
            st.warning("⚠️ conftest.py not found - browser fixture may not be available")
        else:
            debug("Found conftest.py", stage="stage10", operation="conftest_validation",
                  context={'conftest_path': conftest_path})

        # CRITICAL FIX: Copy temporary script to GretahAI_ScriptWeaver directory
        # This ensures pytest uses the correct rootdir where conftest.py is located
        local_script_name = f"temp_stage10_script_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        local_script_path = os.path.join(scriptweaver_dir, local_script_name)

        debug("Copying script to local directory", stage="stage10", operation="file_copy",
              context={'source_path': script_path, 'local_script_path': local_script_path})
        shutil.copy2(script_path, local_script_path)

        # Use the local script path for execution
        execution_script_path = local_script_path

        # Set environment variables for the test run
        env = os.environ.copy()
        env["HEADLESS"] = "0"  # Always run in visible mode for Stage 10
        env["PYTEST_QUIET_MODE"] = "1" if not verbose_mode else "0"

        # Generate timestamped result file name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        result_xml_path = os.path.join(scriptweaver_dir, f"results_stage10_{timestamp}.xml")

        # Build pytest command with proper configuration
        pytest_command = [
            "pytest",
            execution_script_path,  # Use local script path
            f"--junitxml={result_xml_path}",
            "--tb=short",  # Short traceback format
            "-v" if verbose_mode else "-q",  # Verbose or quiet mode
            "--capture=no",  # Don't capture output for better debugging
            f"--rootdir={scriptweaver_dir}",  # Explicitly set rootdir
        ]

        debug("Executing pytest command", stage="stage10", operation="pytest_execution",
              context={
                  'command': ' '.join(pytest_command),
                  'working_directory': scriptweaver_dir,
                  'script_path': execution_script_path,
                  'conftest_path': conftest_path
              })

        # Record start time for execution duration calculation
        execution_start_time = time.time()

        # Execute the test script
        result = subprocess.run(
            pytest_command,
            capture_output=True,
            text=True,
            env=env,
            cwd=scriptweaver_dir  # This is crucial for conftest.py access
        )

        # Calculate execution duration
        execution_duration = time.time() - execution_start_time

        debug("Pytest execution completed", stage="stage10", operation="pytest_execution",
              context={
                  'return_code': result.returncode,
                  'execution_duration': f"{execution_duration:.2f}s",
                  'stdout_length': len(result.stdout),
                  'stderr_length': len(result.stderr)
              })

        # Parse JUnit XML results if available
        xml_results = None
        performance_metrics = {}
        artifacts = {}

        if os.path.exists(result_xml_path):
            try:
                from core.junit_parser import parse_junit_xml, format_test_results_for_display
                xml_results = parse_junit_xml(result_xml_path)
                if xml_results:
                    formatted_results = format_test_results_for_display(xml_results)
                    performance_metrics = formatted_results.get("performance_summary", {})

                    # Extract artifacts from test details
                    for test_detail in formatted_results.get("test_details", []):
                        test_artifacts = test_detail.get("artifacts", {})
                        if test_artifacts:
                            artifacts.update(test_artifacts)
                debug("Successfully parsed JUnit XML results", stage="stage10", operation="junit_parsing")
            except Exception as e:
                debug("Failed to parse JUnit XML", stage="stage10", operation="junit_parsing",
                      context={'error': str(e)})
        else:
            debug("JUnit XML file not found", stage="stage10", operation="junit_parsing",
                  context={'result_xml_path': result_xml_path})

        # Check for screenshots
        screenshots = []
        screenshots_dir = Path(scriptweaver_dir) / "screenshots"
        if screenshots_dir.exists():
            screenshot_files = list(screenshots_dir.glob("*.png"))
            screenshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            screenshots = [str(f) for f in screenshot_files[:5]]  # Get up to 5 most recent
            debug("Found screenshots", stage="stage10", operation="screenshot_detection",
                  context={'screenshot_count': len(screenshots)})

        # Prepare the results dictionary
        execution_results = {
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode,
            "execution_time": execution_duration,  # Add execution time
            "success": result.returncode == 0,
            "screenshots": screenshots,
            "script_path": script_path,
            "test_context": f"Stage 10 generated script ({filename})",
            "xml_path": result_xml_path if os.path.exists(result_xml_path) else None,
            "xml_results": xml_results,
            "performance_metrics": performance_metrics,
            "artifacts": artifacts,
            "timestamp": timestamp,
            "working_directory": scriptweaver_dir,
            "conftest_available": os.path.exists(conftest_path),
            "local_script_path": local_script_path  # Store for cleanup
        }

        debug("Script execution completed successfully", stage="stage10", operation="script_execution")

        # Clean up the temporary local script file
        try:
            if os.path.exists(local_script_path):
                os.unlink(local_script_path)
                debug("Cleaned up temporary local script", stage="stage10", operation="file_cleanup",
                      context={'local_script_path': local_script_path})
        except Exception as cleanup_error:
            debug("Failed to clean up temporary local script", stage="stage10", operation="file_cleanup",
                  context={'error': str(cleanup_error), 'local_script_path': local_script_path})

        return execution_results

    except Exception as e:
        error_msg = f"Failed to execute script with conftest integration: {e}"
        debug("Failed to execute script with conftest integration", stage="stage10", operation="error_handling",
              context={'error': error_msg})

        # Clean up the temporary local script file if it was created
        try:
            if 'local_script_path' in locals() and os.path.exists(local_script_path):
                os.unlink(local_script_path)
                debug("Cleaned up temporary local script after error", stage="stage10", operation="file_cleanup",
                      context={'local_script_path': local_script_path})
        except Exception as cleanup_error:
            debug("Failed to clean up temporary local script after error", stage="stage10", operation="file_cleanup",
                  context={'error': str(cleanup_error), 'local_script_path': local_script_path})

        # Return error results
        return {
            "stdout": "",
            "stderr": error_msg,
            "returncode": -1,
            "execution_time": 0.0,  # Add execution time for error case
            "success": False,
            "screenshots": [],
            "script_path": script_path,
            "test_context": f"Stage 10 generated script ({filename})",
            "xml_path": None,
            "xml_results": None,
            "performance_metrics": {},
            "artifacts": {},
            "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
            "error": str(e),
            "working_directory": os.getcwd(),
            "conftest_available": False
        }


def _handle_failure_analysis(test_results, target_test_case, filename):
    """
    Handle the failure analysis workflow when a script execution fails.

    Args:
        test_results: Test execution results dictionary
        target_test_case: Target test case information
        filename: Generated script filename
    """
    try:
        debug("Starting failure analysis for script", stage="stage10", operation="failure_analysis",
              context={'filename': filename})

        # Check if we have the necessary data for analysis
        if 'stage10_generation_results' not in st.session_state:
            st.error("❌ **Analysis Error**: Generated script data not found in session state.")
            return

        generation_data = st.session_state['stage10_generation_results']
        failed_script_content = generation_data.get('content', '')
        template_script = generation_data.get('template_script', {})

        # Get gap analysis data if available
        gap_analysis_data = generation_data.get('gap_analysis_data')
        gap_responses = generation_data.get('gap_responses')

        if not failed_script_content:
            st.error("❌ **Analysis Error**: Script content not available for analysis.")
            return

        # Import the failure analysis prompt builder
        from core.template_prompt_builder import generate_script_failure_analysis_prompt

        with st.spinner("🔍 Analyzing script failure..."):
            # Generate the failure analysis prompt
            analysis_prompt = generate_script_failure_analysis_prompt(
                failed_script_content=failed_script_content,
                execution_logs=test_results,
                template_script=template_script,
                target_test_case=target_test_case,
                gap_analysis_data=gap_analysis_data,
                gap_responses=gap_responses
            )

            # Call Google AI for failure analysis
            debug("Calling Google AI for script failure analysis", stage="stage10", operation="ai_analysis")
            analysis_response = generate_llm_response(
                prompt=analysis_prompt,
                model_name="gemini-2.0-flash",
                api_key=st.session_state.get('state', {}).google_api_key if hasattr(st.session_state.get('state', {}), 'google_api_key') else None,
                category="script_failure_analysis",
                context={
                    'script_filename': filename,
                    'target_test_case_id': target_test_case.get('Test Case ID', 'unknown'),
                    'template_test_case_id': template_script.get('test_case_id', 'unknown'),
                    'analysis_type': 'execution_failure_analysis'
                }
            )

            if analysis_response:
                # Parse the analysis response
                analysis_data = _parse_failure_analysis_response(analysis_response)

                if analysis_data:
                    # Store analysis results in session state
                    analysis_key = f"stage10_failure_analysis_{filename}"
                    st.session_state[analysis_key] = {
                        'analysis_data': analysis_data,
                        'target_test_case': target_test_case,
                        'template_script': template_script,
                        'failed_script_content': failed_script_content,
                        'gap_analysis_data': gap_analysis_data,
                        'gap_responses': gap_responses,
                        'timestamp': datetime.now().isoformat()
                    }

                    debug("Failure analysis completed and stored", stage="stage10", operation="failure_analysis",
                          context={'filename': filename})

                    # Display analysis results
                    render_failure_analysis_results(analysis_data, target_test_case)

                    # Display regeneration options
                    regenerate_clicked, regeneration_option = render_regeneration_options(analysis_data, filename)

                    if regenerate_clicked and regeneration_option:
                        _handle_enhanced_regeneration(
                            analysis_data, regeneration_option, filename,
                            template_script, target_test_case, gap_analysis_data, gap_responses
                        )
                else:
                    st.error("❌ **Analysis Error**: Failed to parse AI analysis response.")
            else:
                st.error("❌ **Analysis Error**: Failed to get response from AI analysis.")

    except Exception as e:
        error_msg = f"Failed to perform failure analysis: {e}"
        st.error(f"❌ **Analysis Error**: {error_msg}")
        debug("Failure analysis error", stage="stage10", operation="error_handling",
              context={'error': str(e), 'error_type': type(e).__name__})


def _parse_failure_analysis_response(response):
    """
    Parse the AI failure analysis response into structured data.

    Args:
        response: Raw AI response text

    Returns:
        dict: Parsed analysis data or None if parsing fails
    """
    try:
        import json
        import re

        debug("Parsing failure analysis response", stage="stage10", operation="response_parsing")

        # Clean the response and extract JSON
        cleaned_response = clean_llm_response(response)

        # Try to find JSON in the response
        json_match = re.search(r'```json\s*(\{.*?\})\s*```', cleaned_response, re.DOTALL)
        if not json_match:
            # Try without code blocks
            json_match = re.search(r'(\{.*\})', cleaned_response, re.DOTALL)

        if json_match:
            json_str = json_match.group(1)
            analysis_data = json.loads(json_str)

            # Validate required fields
            required_fields = ['failure_analysis', 'improvement_recommendations']
            for field in required_fields:
                if field not in analysis_data:
                    analysis_data[field] = {}

            debug("Successfully parsed failure analysis response", stage="stage10", operation="response_parsing")
            return analysis_data
        else:
            debug("No JSON found in failure analysis response", stage="stage10", operation="response_parsing")
            return None

    except Exception as e:
        debug("Error parsing failure analysis response", stage="stage10", operation="response_parsing",
              context={'error': str(e), 'error_type': type(e).__name__})
        return None


def _handle_enhanced_regeneration(analysis_data, regeneration_option, filename,
                                template_script, target_test_case, gap_analysis_data, gap_responses):
    """
    Handle enhanced script regeneration based on failure analysis.

    Args:
        analysis_data: Parsed failure analysis results
        regeneration_option: Selected regeneration option ('enhanced_targeted' or 'enhanced_inference')
        filename: Original script filename
        template_script: Template script used
        target_test_case: Target test case information
        gap_analysis_data: Original gap analysis data
        gap_responses: Original gap responses
    """
    try:
        debug("Starting enhanced regeneration", stage="stage10", operation="script_regeneration",
              context={'regeneration_option': regeneration_option})

        # Get state manager instance
        state = st.session_state.get('state')
        if not state:
            st.error("❌ **Regeneration Error**: State manager not available.")
            return

        # Create enhanced context based on failure analysis
        enhanced_context = _create_enhanced_context_from_analysis(
            analysis_data, regeneration_option, gap_analysis_data, gap_responses
        )

        # Generate new script with enhanced context
        with st.spinner("🔄 Regenerating script with AI improvements..."):
            _generate_script_from_template(
                state=state,
                template_script=template_script,
                target_test_case=target_test_case,
                custom_instructions=f"Enhanced regeneration based on failure analysis. Option: {regeneration_option}",
                preserve_structure=True,
                include_error_handling=True,
                enhanced_context=enhanced_context
            )

        st.success("✅ **Script Regenerated** with failure analysis improvements!")
        debug("Enhanced regeneration completed", stage="stage10", operation="script_regeneration",
              context={'filename': filename})

    except Exception as e:
        error_msg = f"Failed to perform enhanced regeneration: {e}"
        st.error(f"❌ **Regeneration Error**: {error_msg}")
        debug("Enhanced regeneration error", stage="stage10", operation="error_handling",
              context={'error': str(e), 'error_type': type(e).__name__})


def _create_enhanced_context_from_analysis(analysis_data, regeneration_option, gap_analysis_data, gap_responses):
    """
    Create enhanced context for script regeneration based on failure analysis.

    Args:
        analysis_data: Parsed failure analysis results
        regeneration_option: Selected regeneration option
        gap_analysis_data: Original gap analysis data
        gap_responses: Original gap responses

    Returns:
        dict: Enhanced context for script generation
    """
    try:
        enhanced_context = {
            'failure_analysis': analysis_data,
            'regeneration_option': regeneration_option,
            'gap_analysis': gap_analysis_data,
            'gap_responses': gap_responses
        }

        # Add specific improvements based on analysis
        recommendations = analysis_data.get('improvement_recommendations', {})
        immediate_fixes = recommendations.get('immediate_fixes', [])
        code_improvements = recommendations.get('code_improvements', [])
        regeneration_guidance = recommendations.get('regeneration_guidance', '')

        enhanced_context['immediate_fixes'] = immediate_fixes
        enhanced_context['code_improvements'] = code_improvements
        enhanced_context['regeneration_guidance'] = regeneration_guidance

        # Add new gaps discovered during analysis
        gap_assessment = analysis_data.get('gap_assessment', {})
        new_gaps = gap_assessment.get('new_gaps', [])
        enhanced_context['additional_gaps'] = new_gaps

        debug("Created enhanced context", stage="stage10", operation="context_creation",
              context={'immediate_fixes_count': len(immediate_fixes), 'additional_gaps_count': len(new_gaps)})
        return enhanced_context

    except Exception as e:
        debug("Error creating enhanced context", stage="stage10", operation="error_handling",
              context={'error': str(e), 'error_type': type(e).__name__})
        return {}
