"""
Stage 7: Test Script Execution

This module handles Phase 7 of the GretahAI ScriptWeaver application workflow.
Stage 7 is responsible for executing the generated test scripts and managing
the progression through test case steps.

Key Features:
- Test script execution using pytest with proper environment configuration
- Screenshot capture and display functionality
- Automatic advancement to next test case step after successful execution
- Combined script creation for completed test cases
- Auto-transition preferences and workflow management
- Proper handling of test results (success/failure scenarios)
- Integration with Stage 8 for script optimization
- Enhanced error handling with workflow pause and user acknowledgment
- Detailed error reporting with retry and continue options
- Error state management through StateManager

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Proper workflow transitions (Stage 7 → Stage 8 or Stage 7 → Stage 3)

Functions:
    stage7_run_script(state): Main Stage 7 function for test script execution
    create_combined_script(state): Helper function to create combined scripts
    advance_to_next_step(): Helper function to advance to the next test step

Phase 3c Enhancement: Standardized logging with centralized infrastructure
"""

import os
import logging
import streamlit as st
import subprocess
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any
from state_manager import StateStage

# Note: AI functions for script merging are now handled in Stage 8 optimization

# Enhanced logging configuration using centralized infrastructure
from core.logging_config import get_stage_logger
from debug_utils import debug
logger = get_stage_logger("stage7")


def create_synthetic_original_step(step_table_entry: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a synthetic original step from JSON step data for backward compatibility.

    This function converts JSON step data to the format expected by UI components
    that still reference the original test case step format.

    Args:
        step_table_entry: Step data from JSON storage

    Returns:
        Dict representing an original test case step
    """
    if not step_table_entry:
        return {}

    # Convert JSON step data to original test case format
    synthetic_step = {
        'Step No': step_table_entry.get('step_no', ''),
        'Test Steps': step_table_entry.get('step_description', step_table_entry.get('action', '')),
        'Expected Result': step_table_entry.get('expected_result', ''),
        '_is_synthetic': True,  # Mark as synthetic for debugging
        '_source': 'json_step_data'  # Indicate source
    }

    debug("Created synthetic original step",
          stage="stage7",
          operation="synthetic_step_creation",
          context={
              'step_no': synthetic_step['Step No'],
              'test_steps': synthetic_step['Test Steps'][:100],  # Truncate for logging
              'source': 'json_step_data'
          })
    return synthetic_step


# Note: create_final_script_with_header function available in Stage 6 (stages/stage6.py)
# where script generation logically belongs


def _create_sync_method(state_instance):
    """
    Create the sync_script_path_for_current_step method for an existing state instance.

    This function dynamically adds the synchronization method to existing StateManager
    instances that were created before the method was added to the class.

    Args:
        state_instance: The StateManager instance to add the method to

    Returns:
        Callable: The sync method bound to the state instance
    """
    def sync_script_path_for_current_step():
        """
        Synchronize the generated_script_path with the current step's script file from JSON data.

        Returns:
            bool: True if script path was successfully synchronized, False otherwise
        """
        import logging
        import os
        logger = logging.getLogger("ScriptWeaver.state_manager")

        logger.info("=== sync_script_path_for_current_step() called (dynamic method) ===")

        # Ensure we have a current step selected
        if not hasattr(state_instance, 'selected_step_table_entry') or not state_instance.selected_step_table_entry:
            logger.warning("No current step selected - cannot sync script path")
            return False

        current_step = state_instance.selected_step_table_entry
        step_no = current_step.get('step_no', 'Unknown')

        # Get the script file path from JSON data
        script_file_path = current_step.get('_script_file_path')

        if not script_file_path:
            logger.warning(f"No script file path found in JSON data for step {step_no}")
            return False

        # Verify the script file exists
        if not os.path.exists(script_file_path):
            logger.error(f"Script file does not exist: {script_file_path}")
            logger.error(f"Cannot sync script path for step {step_no}")
            return False

        # Update the generated_script_path if it's different
        old_generated_script_path = getattr(state_instance, 'generated_script_path', '')
        if old_generated_script_path != script_file_path:
            state_instance.generated_script_path = script_file_path
            logger.info(f"State change: generated_script_path synchronized for step {step_no}")
            logger.info(f"  → Old path: {old_generated_script_path}")
            logger.info(f"  → New path: {script_file_path}")

            # Also update last_script_file for consistency
            state_instance.last_script_file = script_file_path
            logger.info(f"State change: last_script_file synchronized: {script_file_path}")

            # Load script content if available
            try:
                with open(script_file_path, 'r') as f:
                    script_content = f.read()
                state_instance.last_script_content = script_content
                logger.info(f"State change: last_script_content loaded ({len(script_content)} chars)")
            except Exception as e:
                logger.warning(f"Could not load script content from {script_file_path}: {e}")

            return True
        else:
            logger.info(f"Script path already synchronized for step {step_no}: {script_file_path}")
            return True

    return sync_script_path_for_current_step


def advance_to_next_step():
    """
    Advance to the next test case step using JSON-based approach.

    This function uses JSON step data storage as the single source of truth,
    eliminating dependency on original test case data for step progression.

    Returns:
        bool: True if successfully advanced to the next test case step, False if all steps are processed
    """
    debug("advance_to_next_step() called (JSON-based)",
          stage="stage7",
          operation="step_advancement")

    # Get state manager
    from state_manager import StateManager
    state = StateManager.get(st)

    # Get effective step table from JSON storage (single source of truth)
    try:
        step_table = state.get_effective_step_table()
        debug("Retrieved step table from JSON storage",
              stage="stage7",
              operation="step_table_retrieval",
              context={'step_count': len(step_table)})
    except ValueError as e:
        debug("Failed to get step table from JSON storage",
              stage="stage7",
              operation="step_table_retrieval",
              context={'error': str(e)})
        return False

    # Check if we have a step table
    if not step_table or not isinstance(step_table, list):
        debug("No valid step table found in JSON storage",
              stage="stage7",
              operation="step_table_validation")
        return False

    # Get the total number of steps from JSON data
    if state.total_steps == 0:
        state.total_steps = len(step_table)
        logger.info(f"State change: total_steps = {state.total_steps} (from JSON)")

    # Log detailed state information before advancing
    logger.info(f"State before advancing: current_step_index = {state.current_step_index}")
    logger.info(f"State before advancing: total_steps = {state.total_steps}")
    logger.info(f"State before advancing: step_ready_for_script = {state.step_ready_for_script}")
    logger.info(f"State before advancing: all_steps_done = {state.all_steps_done}")

    # Also log to Streamlit session state for debugging
    st.session_state['debug_before_advance'] = {
        'current_step_index': state.current_step_index,
        'total_steps': state.total_steps,
        'step_ready_for_script': state.step_ready_for_script,
        'all_steps_done': state.all_steps_done,
        'timestamp': datetime.now().strftime("%H:%M:%S.%f")
    }

    # Save the current step information before advancing (from JSON data)
    current_step_info = None
    if hasattr(state, 'selected_step_table_entry') and state.selected_step_table_entry:
        current_step_info = {
            'step_no': state.selected_step_table_entry.get('step_no', 'Unknown'),
            'action': state.selected_step_table_entry.get('action', ''),
            'expected_result': state.selected_step_table_entry.get('expected_result', ''),
            'step_description': state.selected_step_table_entry.get('step_description', '')
        }
        logger.info(f"Current step before advancing: {current_step_info['step_no']} - {current_step_info['step_description']}")

    # Use the state manager's update method to increment the step index
    new_step_index = state.current_step_index + 1
    state.update_step_progress(current_step_index=new_step_index)

    # Check if we've processed all steps
    if new_step_index >= state.total_steps:
        logger.info("All steps processed, setting all_steps_done to True")
        # Set all_steps_done to True only if we're actually on the last step
        # This ensures we don't prematurely mark all steps as done
        if state.current_step_index == state.total_steps - 1:
            state.update_step_progress(all_steps_done=True)
            logger.info(f"Setting all_steps_done=True on last step (index {state.current_step_index}, total {state.total_steps})")
        else:
            logger.warning(f"Not setting all_steps_done=True because we're not on the last step (index {state.current_step_index}, total {state.total_steps})")

        # Add a message to session state for display after rerun
        st.session_state['stage_progression_message'] = "✅ All test case steps have been processed!"

        # All steps completed - workflow will show manual progression options in Stage 7 UI
        logger.info("All steps completed - user will choose next action in Stage 7 UI")

        # Force state update in session state
        st.session_state['state'] = state

        # Immediately rerun to refresh the UI with the new state
        st.rerun()
        # Return statement will never be reached due to rerun, but included for clarity
        return False

    # Get the next step from the JSON step table (single source of truth)
    next_step = step_table[state.current_step_index]
    logger.info(f"Next step from JSON: step_no = {next_step.get('step_no')}, action = {next_step.get('action')}")
    logger.info(f"Next step description: {next_step.get('step_description', 'No description')}")

    # Create or update the original step representation for backward compatibility
    # This ensures existing UI components that expect 'selected_step' continue to work
    selected_original_step = create_synthetic_original_step(next_step)

    if selected_original_step and next_step:
            # Save context from the current step before updating state
            if current_step_info:
                # Mark the current step as completed (using JSON step number)
                current_step_no = current_step_info['step_no']

                # Initialize completed_steps if not present
                if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                    state.completed_steps = []

                # Add current step to completed steps if not already there
                if current_step_no not in state.completed_steps:
                    state.completed_steps.append(current_step_no)
                    logger.info(f"State change: added step {current_step_no} to completed_steps (from JSON)")

                # Store context from the current step that might be useful for the next step
                if not hasattr(state, 'step_context') or not isinstance(state.step_context, dict):
                    state.step_context = {}

                # Save relevant context from the current step
                state.step_context[current_step_no] = {
                    "elements": state.step_elements if hasattr(state, 'step_elements') else [],
                    "matches": state.step_matches if hasattr(state, 'step_matches') else {},
                    "test_data": state.test_data if hasattr(state, 'test_data') else {},
                    "script_path": state.generated_script_path if hasattr(state, 'generated_script_path') else None
                }
                # Log state change for context saving
                previous_context = state.step_context.get(current_step_no, None)
                if previous_context != state.step_context[current_step_no]:
                    logger.info(f"State change: saved context for step {current_step_no}")

            # Update state manager with the new step
            state.selected_step_table_entry = next_step
            state.selected_step = selected_original_step
            # Log state change for step selection
            previous_step_no = state.selected_step.get('Step No') if hasattr(state, 'selected_step') and state.selected_step else None
            new_step_no = selected_original_step.get('Step No')
            if previous_step_no != new_step_no:
                logger.info(f"State change: updated selected_step_table_entry and selected_step to Step No {new_step_no}")

            # CRITICAL FIX: Synchronize script path for the new step
            # This ensures Stage 7 uses the correct script file for the current step
            logger.info("=== SYNCHRONIZING SCRIPT PATH FOR NEW STEP ===")

            # Check if the method exists, if not, add it dynamically
            if not hasattr(state, 'sync_script_path_for_current_step'):
                logger.info("Adding sync_script_path_for_current_step method to existing state instance")
                state.sync_script_path_for_current_step = _create_sync_method(state)

            sync_success = state.sync_script_path_for_current_step()
            if sync_success:
                logger.info(f"✅ Script path synchronized for step {new_step_no}: {state.generated_script_path}")
            else:
                logger.warning(f"⚠️ Failed to sync script path for step {new_step_no}")

            # Reset step-specific state variables
            state.step_elements = []
            state.step_matches = {}
            state.test_data = {}
            state.test_data_skipped = False
            state.llm_step_analysis = {}
            state.step_ready_for_script = False
            state.script_just_generated = False
            # Only log if there were actual changes to reset
            if (hasattr(state, 'step_elements') and state.step_elements) or \
               (hasattr(state, 'step_matches') and state.step_matches) or \
               (hasattr(state, 'test_data') and state.test_data) or \
               (hasattr(state, 'test_data_skipped') and state.test_data_skipped) or \
               (hasattr(state, 'llm_step_analysis') and state.llm_step_analysis) or \
               (hasattr(state, 'step_ready_for_script') and state.step_ready_for_script) or \
               (hasattr(state, 'script_just_generated') and state.script_just_generated):
                logger.info("State change: reset step-specific state variables")

            # Log detailed state information after advancing
            logger.info(f"State after advancing: current_step_index = {state.current_step_index}")
            logger.info(f"State after advancing: total_steps = {state.total_steps}")
            logger.info(f"State after advancing: step_ready_for_script = {state.step_ready_for_script}")
            logger.info(f"State after advancing: all_steps_done = {state.all_steps_done}")
            logger.info(f"State after advancing: selected_step.Step No = {state.selected_step.get('Step No')}")

            # Also log to Streamlit session state for debugging
            st.session_state['debug_after_advance'] = {
                'current_step_index': state.current_step_index,
                'total_steps': state.total_steps,
                'step_ready_for_script': state.step_ready_for_script,
                'all_steps_done': state.all_steps_done,
                'selected_step_no': state.selected_step.get('Step No'),
                'timestamp': datetime.now().strftime("%H:%M:%S.%f")
            }

            # Add a message to session state for display after rerun (using JSON data)
            next_step_no = next_step.get('step_no')
            next_step_description = next_step.get('step_description', next_step.get('action', ''))[:50]  # Truncate for display
            st.session_state['stage_progression_message'] = f"✅ Advanced to Test Case Step {next_step_no}: {next_step_description}..."

            # Force the state to be updated in the session state
            st.session_state['state'] = state

            logger.info(f"Successfully advanced to next step: {next_step_no} (JSON-based)")
            # Immediately rerun to refresh the UI with the new state
            st.rerun()
            # Return statement will never be reached due to rerun, but included for clarity
            return True
    else:
        logger.warning(f"Failed to advance: selected_original_step={selected_original_step is not None}, next_step={next_step is not None}")
        logger.warning("This should not happen with JSON-based approach")

    logger.warning("Failed to advance to next step")
    return False


def stage7_run_script(state):
    """
    Phase 7: Run Test Script for Selected Test Case Step.

    This stage allows the user to run the generated test script for the selected test case step.
    It checks if a script has been generated, displays the script, and provides a simple button
    to run the test. When the user clicks the button, it executes the script using pytest and
    displays the results, including any screenshots captured during the test run.

    Args:
        state (StateManager): The application state manager instance
    """
    import os  # Add local import to ensure os module is available in this function
    st.markdown("<h2 class='stage-header'>Phase 7: Run Test Script</h2>", unsafe_allow_html=True)

    # Check for execution errors that need user acknowledgment
    if (_has_unacknowledged_error(state)):
        _display_error_acknowledgment_ui(state)
        return

    # CRITICAL FIX: Ensure script path is synchronized with current step
    # This handles cases where users navigate directly to Stage 7 or reload the page
    if hasattr(state, 'selected_step_table_entry') and state.selected_step_table_entry:
        logger.info("=== STAGE 7 ENTRY: SYNCHRONIZING SCRIPT PATH ===")

        # Check if the method exists, if not, add it dynamically
        if not hasattr(state, 'sync_script_path_for_current_step'):
            logger.info("Adding sync_script_path_for_current_step method to existing state instance")
            state.sync_script_path_for_current_step = _create_sync_method(state)

        sync_success = state.sync_script_path_for_current_step()
        if sync_success:
            current_step_no = state.selected_step_table_entry.get('step_no', 'Unknown')
            logger.info(f"✅ Stage 7 entry: Script path synchronized for step {current_step_no}")
            logger.info(f"  → Script path from JSON: {state.selected_step_table_entry.get('_script_file_path', 'None')}")
            logger.info(f"  → State generated_script_path: {getattr(state, 'generated_script_path', 'None')}")
            logger.info(f"  → State last_script_file: {getattr(state, 'last_script_file', 'None')}")

            # CRITICAL FIX: Additional validation that script file exists
            script_path = getattr(state, 'generated_script_path', '')
            if script_path and os.path.exists(script_path):
                logger.info(f"✅ Script file verified to exist: {script_path}")
            else:
                logger.error(f"❌ CRITICAL: Script file does not exist: {script_path}")
        else:
            logger.warning("⚠️ Stage 7 entry: Failed to sync script path")

    if not hasattr(state, 'generated_script_path') or not state.generated_script_path:
        st.warning("⚠️ Please generate a test script in Phase 6 first")
        return

    # Check if all steps are completed BEFORE showing the run button
    # This ensures the Stage 8 transition UI is always visible when appropriate
    if hasattr(state, 'all_steps_done') and state.all_steps_done:
        logger.info("Stage 7: All steps completed, showing Stage 8 transition options")

        st.success("✅ All test case steps have been processed!")

        # Note: Combined script creation is now handled in Stage 6
        # Display combined script if it exists from Stage 6
        if hasattr(state, 'combined_script_path') and state.combined_script_path:
            st.success(f"📄 Combined script available: {os.path.basename(state.combined_script_path)}")

            # Add combined script to history for Stage 9 browsing
            try:
                if hasattr(state, 'combined_script_content') and state.combined_script_content:
                    combined_content = state.combined_script_content
                else:
                    # Load from file if content not in state
                    try:
                        with open(state.combined_script_path, 'r') as f:
                            combined_content = f.read()
                    except Exception as e:
                        logger.warning(f"Could not read combined script for history: {e}")
                        combined_content = ""

                if combined_content:
                    state.add_script_to_history(
                        script_content=combined_content,
                        script_type='combined',
                        step_no=None,
                        file_path=state.combined_script_path,
                        metadata={
                            'test_case_id': state.selected_test_case.get('Test Case ID', 'unknown'),
                            'total_steps': getattr(state, 'total_steps', 0),
                            'generation_timestamp': datetime.now().isoformat(),
                            'all_steps_complete': True
                        }
                    )
                    logger.info("Added combined script to history for browsing")
            except Exception as e:
                logger.warning(f"Failed to add combined script to history: {e}")

            with st.expander("View Combined Script", expanded=False):
                if hasattr(state, 'combined_script_content') and state.combined_script_content:
                    st.code(state.combined_script_content, language="python")
                else:
                    # Load from file if content not in state
                    try:
                        with open(state.combined_script_path, 'r') as f:
                            combined_script_content = f.read()
                        st.code(combined_script_content, language="python")
                    except Exception as e:
                        st.error(f"Error reading combined script: {e}")
                st.info(f"Combined script file: {state.combined_script_path}")
        else:
            st.info("📄 Combined script will be created automatically in Stage 6")

        # Show workflow completion message and manual progression options
        st.markdown("""
        <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
            <p style="font-size: 16px; color: #4CAF50; margin: 0;">All steps completed for this test case</p>
            <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">Choose your next action below</p>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("### Choose Next Action")
        st.markdown("You can either optimize the combined script or return to select a new test case:")

        # Create two columns for the progression options
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**Recommended:** Optimize your test script")
            st.markdown("Creates a production-ready, consolidated test suite")
            if st.button("Proceed to Script Optimization (Phase 8)", use_container_width=True, type="primary"):
                logger.info("User chose to proceed to Script Optimization (Phase 8)")

                # Use centralized stage management for Stage 7 -> Stage 8 transition
                state.advance_to(StateStage.STAGE8_OPTIMIZE, "User chose to proceed to script optimization")

                # Force state update in session state
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ All steps completed. Proceeding to Script Optimization (Phase 8)."

                # Use one-shot flag to indicate we're transitioning from Stage 7 to Stage 8
                from utils.flag_helpers import one_shot_flag
                with one_shot_flag('transitioning_to_stage8'):
                    logger.info("Setting one-shot transitioning_to_stage8 flag for Stage 7 -> Stage 8 transition")
                    st.rerun()

        with col2:
            st.markdown("**Alternative:** Skip optimization")
            st.markdown("Return to test case selection immediately")
            if st.button("Return to Test Case Selection (Phase 3)", use_container_width=True):
                logger.info("User chose to skip optimization and return to Phase 3")

                # Reset test case state with confirmation (this will also handle stage transition)
                state.reset_test_case_state(confirm=True, reason="User chose to skip optimization and return to Phase 3")

                # Set a flag to indicate we're transitioning from Stage 7 to Stage 3
                st.session_state['transitioning_to_stage3'] = True
                st.session_state['stage_progression_message'] = "✅ All steps completed. Returning to Phase 3 to select a new test case."

                # Force state update in session state
                st.session_state['state'] = state
                st.rerun()

        # Return early to prevent showing the run button when all steps are done
        return

    # Create two columns for script info and test status
    col1, col2 = st.columns([3, 2])

    with col1:
        st.success(f"✓ Script ready: {os.path.basename(state.generated_script_path)}")

    with col2:
        # Show test case step info
        step_no = state.selected_step.get('Step No', 'Unknown')
        st.info(f"Test Case Step: {step_no}")

    # Show information when we're on the last step
    if state.current_step_index == state.total_steps - 1:
        st.info("📌 This is the last step in the test case.")

    # Display the generated script in a collapsible section
    if hasattr(state, 'last_script_content') and state.last_script_content:
        with st.expander("View Test Script", expanded=False):
            st.code(state.last_script_content, language="python")
            if hasattr(state, 'last_script_file') and state.last_script_file:
                st.info(f"Script file: {state.last_script_file}")

    # Add a visual indicator to encourage running the script
    st.markdown("""
    <div style="text-align: center; margin: 20px 0; padding: 10px; background-color: #e8f5e9; border-radius: 10px; border: 1px solid #4CAF50;">
        <p style="font-size: 16px; color: #4CAF50; margin: 0;">Click the button below to run the test script</p>
        <p style="font-size: 14px; color: #4CAF50; margin: 5px 0 0 0;">This will open a browser window to execute the test</p>
    </div>
    """, unsafe_allow_html=True)

    # Verbose mode toggle
    col1, col2 = st.columns([3, 1])
    with col1:
        run_button = st.button("Run Test Script", disabled=not state.generated_script_path, use_container_width=True)
    with col2:
        verbose_mode = st.checkbox("Verbose Output",
                                 value=st.session_state.get('stage7_verbose_mode', False),
                                 help="Show detailed execution logs and performance metrics")
        st.session_state['stage7_verbose_mode'] = verbose_mode

    # Enhanced run button with comprehensive pytest execution
    if run_button:
        with st.spinner(f"Running test script for Step {state.selected_step.get('Step No')}..."):
            try:
                # Import the JUnit parser
                from core.junit_parser import parse_junit_xml, format_test_results_for_display

                # Set environment variables for the test run
                env = os.environ.copy()
                # Always run in visible mode (not headless)
                env["HEADLESS"] = "0"
                # Set quiet mode based on verbose setting
                env["PYTEST_QUIET_MODE"] = "0" if verbose_mode else "1"

                # Generate timestamped result file name
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                result_xml_path = f"results_{timestamp}.xml"

                # Check if verbose mode is enabled (default: quiet mode for cleaner output)
                verbose_mode = st.session_state.get('stage7_verbose_mode', False)

                if verbose_mode:
                    # Verbose mode: comprehensive options (matching Test Insight)
                    pytest_command = [
                        "pytest",
                        state.generated_script_path,
                        f"--junitxml={result_xml_path}",
                        "--log-cli-level=DEBUG",
                        "--capture=sys",
                        "--tb=short",
                        "-v"
                    ]
                else:
                    # Quiet mode: minimal console output, detailed file logging
                    pytest_command = [
                        "pytest",
                        state.generated_script_path,
                        f"--junitxml={result_xml_path}",
                        "--log-cli-level=WARNING",  # Only show warnings and errors in console
                        "--capture=no",  # Capture output for cleaner console
                        "--tb=short",  # Short traceback format
                        "-q"  # Quiet mode - minimal output
                    ]

                logger.info(f"Stage 7: Executing pytest command: {' '.join(pytest_command)}")
                logger.info(f"Stage 7: Working directory: {os.getcwd()}")
                logger.info(f"Stage 7: Environment HEADLESS: {env.get('HEADLESS', 'not set')}")

                # CRITICAL FIX: Log detailed script path information before execution
                logger.info("=== SCRIPT EXECUTION DETAILS ===")
                logger.info(f"Script path being executed: {state.generated_script_path}")
                logger.info(f"Script file exists: {os.path.exists(state.generated_script_path)}")
                if os.path.exists(state.generated_script_path):
                    # Log file modification time to help identify if it's the latest version
                    import os.path
                    mod_time = os.path.getmtime(state.generated_script_path)
                    mod_datetime = datetime.fromtimestamp(mod_time)
                    logger.info(f"Script file last modified: {mod_datetime.isoformat()}")

                    # Log first few lines of script to verify content
                    try:
                        with open(state.generated_script_path, 'r') as f:
                            first_lines = [f.readline().strip() for _ in range(3)]
                        logger.info(f"Script content preview (first 3 lines): {first_lines}")
                    except Exception as e:
                        logger.warning(f"Could not read script content for preview: {e}")
                else:
                    logger.error(f"❌ CRITICAL: Script file does not exist at execution time!")
                logger.info("=== END SCRIPT EXECUTION DETAILS ===")

                # Run the test script with enhanced configuration
                result = subprocess.run(
                    pytest_command,
                    capture_output=True, text=True,
                    env=env,
                    cwd=os.getcwd()  # Ensure we're in the correct directory
                )

                # Parse JUnit XML results if available
                xml_results = None
                performance_metrics = {}
                artifacts = {}

                if os.path.exists(result_xml_path):
                    xml_results = parse_junit_xml(result_xml_path)
                    if xml_results:
                        formatted_results = format_test_results_for_display(xml_results)
                        performance_metrics = formatted_results.get("performance_summary", {})

                        # Extract artifacts from test details
                        for test_detail in formatted_results.get("test_details", []):
                            test_artifacts = test_detail.get("artifacts", {})
                            if test_artifacts:
                                artifacts.update(test_artifacts)

                # Store comprehensive test results
                state.test_results = {
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode,
                    "step_no": state.selected_step.get('Step No'),
                    "test_case_id": state.selected_test_case.get('Test Case ID'),
                    "xml_path": result_xml_path if os.path.exists(result_xml_path) else None,
                    "xml_results": xml_results,
                    "performance_metrics": performance_metrics,
                    "artifacts": artifacts,
                    "timestamp": timestamp
                }

                # Display the test results with appropriate level of detail
                if result.returncode == 0:
                    st.success(f"✅ Test Case Step {state.selected_step.get('Step No')} test passed!")

                    # Show basic success metrics
                    if xml_results and "summary" in xml_results:
                        duration = xml_results["summary"].get("duration", 0)
                        tests_run = xml_results["summary"].get("tests", 0)
                        st.info(f"🎯 Executed {tests_run} test(s) in {duration:.2f} seconds")

                    # Capture and store URL tracking information after successful test execution
                    try:
                        # Use formatted_results which contains the test_details with properties
                        if xml_results:
                            formatted_results = format_test_results_for_display(xml_results)
                            _capture_and_store_url_tracking(state, formatted_results)
                        else:
                            logger.warning("No XML results available for URL tracking")
                    except Exception as url_tracking_error:
                        logger.warning(f"Failed to capture URL tracking information: {url_tracking_error}")

                    # Set the step_ready_for_script flag to True after successful test execution
                    if not state.step_ready_for_script:
                        state.step_ready_for_script = True
                        logger.info("State change: step_ready_for_script = True (after successful test execution)")

                    # Force state update in session state
                    st.session_state['state'] = state
                else:
                    # Test execution failed - set error state and pause workflow
                    st.error(f"❌ Test Case Step {state.selected_step.get('Step No')} test failed. See details below.")

                    # Show essential error information immediately
                    if xml_results and "summary" in xml_results:
                        failures = xml_results["summary"].get("failures", 0)
                        errors = xml_results["summary"].get("errors", 0)
                        if failures > 0:
                            st.error(f"💥 {failures} test failure(s) occurred")
                        if errors > 0:
                            st.error(f"⚠️ {errors} test error(s) occurred")

                    # Prepare error details for state management
                    error_details = {
                        'error_message': f"Test Case Step {state.selected_step.get('Step No')} execution failed",
                        'returncode': result.returncode,
                        'stdout': result.stdout,
                        'stderr': result.stderr,
                        'timestamp': timestamp,
                        'step_no': state.selected_step.get('Step No', 'Unknown'),
                        'script_path': state.generated_script_path
                    }

                    # Set error state to pause workflow
                    _set_execution_error_safe(state, error_details)

                    # Force state update in session state
                    st.session_state['state'] = state

                # Display test results based on verbose mode setting
                if verbose_mode:
                    # Verbose mode: Show comprehensive test results
                    with st.expander("📊 Detailed Test Execution Results", expanded=True):
                        # Basic execution info
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("Exit Code", result.returncode)
                        with col2:
                            st.metric("Timestamp", timestamp)
                        with col3:
                            if xml_results and "summary" in xml_results:
                                duration = xml_results["summary"].get("duration", 0)
                                st.metric("Duration", f"{duration:.2f}s")

                        # Performance metrics if available
                        if performance_metrics and "aggregated" in performance_metrics:
                            st.subheader("Performance Metrics")
                            perf_cols = st.columns(4)
                            metrics_data = performance_metrics["aggregated"]

                            if "execution_time" in metrics_data:
                                with perf_cols[0]:
                                    st.metric("Execution Time", f"{metrics_data['execution_time']['average']:.3f}s")
                            if "memory_usage" in metrics_data:
                                with perf_cols[1]:
                                    st.metric("Memory Usage", f"{metrics_data['memory_usage']['average']:.1f}MB")
                            if "cpu_usage" in metrics_data:
                                with perf_cols[2]:
                                    st.metric("CPU Usage", f"{metrics_data['cpu_usage']['average']:.1f}%")
                            if "network_requests" in metrics_data:
                                with perf_cols[3]:
                                    st.metric("Network Requests", int(metrics_data['network_requests']['total']))

                        # Console output
                        st.subheader("Console Output")
                        if result.stdout:
                            st.code(result.stdout, language="text")

                        if result.stderr:
                            st.subheader("Error Output")
                            st.code(result.stderr, language="text")
                else:
                    # Quiet mode: show minimal essential information
                    if result.returncode != 0:
                        # Only show detailed info for failures in quiet mode
                        with st.expander("❌ Execution Error Details", expanded=True):
                            # Show only essential error information
                            if result.stderr:
                                st.subheader("Error Output")
                                # Show only the last few lines of stderr for brevity
                                stderr_lines = result.stderr.strip().split('\n')
                                if len(stderr_lines) > 10:
                                    st.code('\n'.join(stderr_lines[-10:]), language="text")
                                    st.caption(f"Showing last 10 lines of error output. Full output available in verbose mode.")
                                else:
                                    st.code(result.stderr, language="text")

                            # Show key failure information from stdout
                            if result.stdout and ("FAILED" in result.stdout or "ERROR" in result.stdout):
                                st.subheader("Test Failure Summary")
                                # Extract and show only failure lines
                                stdout_lines = result.stdout.split('\n')
                                failure_lines = [line for line in stdout_lines if
                                               "FAILED" in line or "ERROR" in line or
                                               "AssertionError" in line or "TimeoutException" in line]
                                if failure_lines:
                                    st.code('\n'.join(failure_lines[:5]), language="text")  # Show first 5 failure lines
                                    if len(failure_lines) > 5:
                                        st.caption(f"Showing first 5 failure lines. {len(failure_lines) - 5} more available in verbose mode.")
                    else:
                        # Success case: show minimal summary
                        with st.expander("✅ Execution Summary", expanded=False):
                            col1, col2 = st.columns(2)
                            with col1:
                                st.metric("Status", "✅ Passed")
                            with col2:
                                if xml_results and "summary" in xml_results:
                                    duration = xml_results["summary"].get("duration", 0)
                                    st.metric("Duration", f"{duration:.2f}s")

                        # Artifacts section (only in verbose mode within the expander)
                        if artifacts:
                            st.subheader("Test Artifacts")
                            for artifact_type, artifact_path in artifacts.items():
                                if os.path.exists(artifact_path):
                                    if artifact_type == "screenshot":
                                        st.image(artifact_path, caption=f"Screenshot: {os.path.basename(artifact_path)}")
                                    elif artifact_type == "log":
                                        # Display log file info without nested expander
                                        st.info(f"📄 Log File: {os.path.basename(artifact_path)}")
                                        try:
                                            with open(artifact_path, 'r', encoding='utf-8') as f:
                                                log_content = f.read()
                                            # Show first few lines as preview
                                            log_lines = log_content.split('\n')
                                            if len(log_lines) > 10:
                                                preview = '\n'.join(log_lines[:10]) + f"\n... ({len(log_lines) - 10} more lines)"
                                                st.code(preview, language="text")
                                                st.caption(f"Full log available at: {artifact_path}")
                                            else:
                                                st.code(log_content, language="text")
                                        except Exception as e:
                                            st.error(f"Could not read log file: {e}")
                                    else:
                                        st.info(f"{artifact_type.title()}: {artifact_path}")
                                else:
                                    st.warning(f"{artifact_type.title()} file not found: {artifact_path}")

                # Display log files in separate expanders (only in verbose mode)
                if verbose_mode and artifacts:
                    log_artifacts = {k: v for k, v in artifacts.items() if k == "log" and os.path.exists(v)}
                    for artifact_type, artifact_path in log_artifacts.items():
                        with st.expander(f"📄 View Full Log: {os.path.basename(artifact_path)}", expanded=False):
                            try:
                                with open(artifact_path, 'r', encoding='utf-8') as f:
                                    st.code(f.read(), language="text")
                            except Exception as e:
                                st.error(f"Could not read log file: {e}")

                # Check for screenshots in the screenshots directory (always show, but adjust prominence)
                screenshots_dir = Path("screenshots")
                if screenshots_dir.exists():
                    screenshot_files = list(screenshots_dir.glob("*.png"))
                    if screenshot_files:
                        # Get the most recent screenshot
                        latest_screenshot = max(screenshot_files, key=os.path.getmtime)

                        # Check if it was created recently (within the last minute)
                        import time
                        if time.time() - os.path.getmtime(latest_screenshot) < 60:
                            # In verbose mode, expand by default; in quiet mode, collapse by default
                            screenshot_expanded = verbose_mode
                            screenshot_title = "📸 Latest Screenshot" if verbose_mode else "📸 Screenshot"

                            with st.expander(screenshot_title, expanded=screenshot_expanded):
                                st.image(str(latest_screenshot), caption=f"Screenshot: {latest_screenshot.name}")
                                if verbose_mode:
                                    st.info(f"Screenshot saved to: {latest_screenshot}")

                logger.info(f"Stage 7: Test execution completed with return code {result.returncode}")

                # Check if execution failed and workflow should be paused
                if _has_unacknowledged_error(state):
                    logger.info("Stage 7: Execution error occurred, pausing workflow for user acknowledgment")
                    # Don't proceed with automatic advancement - let user acknowledge error first
                    return

                # Set flags to indicate the current step is completed and ready for next step
                # Check if there are more steps
                next_step_index = state.current_step_index + 1
                if next_step_index < state.total_steps:
                        # Get the next step information from effective step table
                        effective_step_table = state.get_effective_step_table() if hasattr(state, 'get_effective_step_table') else state.step_table_json
                        next_step = effective_step_table[next_step_index]
                        next_step_no = next_step.get('step_no', 'N/A')

                        # Mark Stage 7 as complete (using JSON step data)
                        current_step_no = state.selected_step_table_entry.get('step_no', 'Unknown') if state.selected_step_table_entry else 'Unknown'
                        st.success(f"✅ Test Case Step {current_step_no} completed successfully!")
                        st.info("Automatically advancing to the next test case step...")

                        # Add a small delay to allow the user to see the success message
                        time.sleep(1.5)

                        # Store current step completion status
                        current_step_no = state.selected_step.get('Step No')
                        if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                            state.completed_steps = []
                        if current_step_no not in state.completed_steps:
                            state.completed_steps.append(current_step_no)
                            logger.info(f"State change: added step {current_step_no} to completed_steps")

                        # The flag for script generation should already be set to True after test execution
                        # Double-check to make sure it's set
                        if not state.step_ready_for_script:
                            state.step_ready_for_script = True
                            logger.info("State change: step_ready_for_script = True (before automatic advancement)")

                        # Log the automatic advancement
                        logger.info(f"Automatically advancing to next step after test execution: {next_step_no}")

                        # Set up session state variables before calling advance_to_next_step
                        # since it will trigger a rerun if successful

                        # Set a message to be displayed in Stage 4 (using JSON step data)
                        st.session_state['stage_progression_message'] = f"✅ Test Case Step {current_step_no} completed and automatically advanced to Step {next_step_no}"

                        # Add debug information to track the state update
                        st.session_state['auto_advance_debug'] = {
                            'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                            'from_step': current_step_no,
                            'to_step': next_step_no,
                            'current_step_index': state.current_step_index
                        }

                        # Set a flag to force a refresh after advancement
                        st.session_state['force_refresh_after_advance'] = {
                            'timestamp': datetime.now().strftime("%H:%M:%S.%f"),
                            'target_step': next_step_no,
                            'from_step': current_step_no
                        }

                        # Use centralized stage management for Stage 7 -> Stage 4 transition
                        state.advance_to(StateStage.STAGE4_DETECT, f"Script execution completed, advancing to step {next_step_no}")

                        # Force state update in session state
                        st.session_state['state'] = state

                        # Add a more visible indicator that we're advancing
                        st.success("🔄 Advancing to the next step... Please wait.")

                        # Add a small delay to ensure the state is properly updated
                        time.sleep(0.5)

                        # Use one-shot flag to indicate we're coming back from Stage 7
                        from utils.flag_helpers import one_shot_flag
                        with one_shot_flag('coming_from_stage7'):
                            logger.info(f"Setting one-shot coming_from_stage7 flag to return to Stage 4 with step {next_step_no}")
                            # Call advance_to_next_step to move to the next step
                            # This will call st.rerun() internally if successful
                            advance_to_next_step()

                        # The code below will only execute if advance_to_next_step() fails
                        # and doesn't trigger a rerun
                        st.error("Failed to automatically advance to the next step. Please check the logs.")
                        logger.error("Failed to automatically advance to the next step after test execution")
                else:
                    # All steps processed - set the flag and automatically advance to Stage 8
                    logger.info("Stage 7: All steps completed, setting all_steps_done flag")
                    state.all_steps_done = True

                    # Store current step completion status
                    current_step_no = state.selected_step.get('Step No')
                    if not hasattr(state, 'completed_steps') or not isinstance(state.completed_steps, list):
                        state.completed_steps = []
                    if current_step_no not in state.completed_steps:
                        state.completed_steps.append(current_step_no)
                        logger.info(f"State change: added final step {current_step_no} to completed_steps")

                    # Show a brief success message before automatic transition
                    st.success("✅ All test case steps have been processed! Automatically proceeding to Script Optimization...")

                    # Automatically advance to Stage 8 since all steps are completed
                    if state.current_stage == StateStage.STAGE7_EXECUTE:
                        state.advance_to(StateStage.STAGE8_OPTIMIZE, "All test steps completed - automatically advancing to Stage 8")

                        # Force state update in session state
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = "✅ All test steps completed. Proceeding to Script Optimization (Stage 8)."

                        logger.info("Stage 7: Automatically advancing to Stage 8 after all steps completed")

                        # Call st.rerun() to immediately refresh the UI
                        st.rerun()
                        return

                    # Fallback: Force state update in session state and rerun to show Stage 8 transition UI
                    st.session_state['state'] = state
                    logger.info("State change: all_steps_done = True, forcing rerun to show Stage 8 transition UI")

                    # Force rerun to show the Stage 8 transition UI at the top level
                    st.rerun()
            except Exception as e:
                # Handle Python exceptions during script execution
                st.error(f"Error running test script: {e}")
                import traceback
                traceback_str = traceback.format_exc()
                st.error(traceback_str)

                # Prepare error details for state management
                error_details = {
                    'error_message': f"Python exception during script execution: {str(e)}",
                    'returncode': -1,
                    'stdout': '',
                    'stderr': traceback_str,
                    'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
                    'step_no': state.selected_step.get('Step No', 'Unknown'),
                    'script_path': state.generated_script_path,
                    'exception_type': type(e).__name__
                }

                # Set error state to pause workflow
                _set_execution_error_safe(state, error_details)

                # Force state update in session state
                st.session_state['state'] = state

                logger.error(f"Python exception during script execution: {e}")
                logger.error(f"Traceback: {traceback_str}")

                # Return to let error acknowledgment UI handle the error
                return


def _display_error_acknowledgment_ui(state):
    """
    Display error acknowledgment UI when script execution fails.

    This function shows detailed error information and provides options for the user
    to acknowledge the error, retry execution, or view full logs before proceeding.

    Args:
        state (StateManager): The application state manager instance
    """
    import logging
    import os
    logger = logging.getLogger("ScriptWeaver.stage7")

    st.error("🚨 **Test Script Execution Failed**")
    st.markdown("The test script execution encountered an error. Please review the details below and choose how to proceed.")

    # Get error details from state
    error_details = getattr(state, 'execution_error_details', {})
    step_no = error_details.get('step_no', 'Unknown')
    error_message = error_details.get('error_message', 'Unknown error occurred')
    returncode = error_details.get('returncode', -1)
    timestamp = error_details.get('timestamp', 'Unknown')

    # Display error summary
    with st.expander("📋 Error Summary", expanded=True):
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Failed Step", step_no)
        with col2:
            st.metric("Exit Code", returncode)
        with col3:
            st.metric("Timestamp", timestamp)

        st.markdown(f"**Error Message:** {error_message}")

    # Display detailed error information
    with st.expander("🔍 Detailed Error Information", expanded=True):
        # Standard output
        stdout = error_details.get('stdout', '')
        if stdout:
            st.subheader("Standard Output")
            st.code(stdout, language="text")

        # Standard error
        stderr = error_details.get('stderr', '')
        if stderr:
            st.subheader("Error Output")
            st.code(stderr, language="text")

        # Script path
        script_path = error_details.get('script_path', '')
        if script_path:
            st.subheader("Failed Script")
            st.info(f"Script Path: {script_path}")

    # Action buttons
    st.markdown("---")
    st.markdown("### Choose how to proceed:")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown("**Continue Workflow**")
        st.markdown("Acknowledge the error and return to test case selection")
        if st.button("✅ Acknowledge Error & Continue", key="acknowledge_error", use_container_width=True):
            logger.info(f"User acknowledged execution error for step {step_no}")

            # Mark error as acknowledged
            _acknowledge_execution_error_safe(state)

            # Clear error state
            _clear_execution_error_safe(state)

            # Check if all steps are completed after acknowledging the error
            if hasattr(state, 'all_steps_done') and state.all_steps_done:
                # All steps completed with error acknowledgment - automatically advance to Stage 8
                if state.current_stage == StateStage.STAGE7_EXECUTE:
                    state.advance_to(StateStage.STAGE8_OPTIMIZE, f"Error acknowledged for step {step_no}, all steps completed - advancing to Stage 8")

                    # Force state update in session state
                    st.session_state['state'] = state
                    st.session_state['stage_progression_message'] = f"⚠️ Step {step_no} error acknowledged. All steps completed. Proceeding to Script Optimization (Stage 8)."

                    logger.info(f"Stage 7: Error acknowledged, all steps completed, automatically advancing to Stage 8")

                    # Call st.rerun() to immediately refresh the UI
                    st.rerun()
                    return

            # Force state update in session state
            st.session_state['state'] = state
            st.session_state['stage_progression_message'] = f"⚠️ Step {step_no} execution failed. Error acknowledged."

            # Use one-shot flag to return to Stage 4
            from utils.flag_helpers import one_shot_flag
            with one_shot_flag('coming_from_stage7'):
                logger.info("Setting one-shot coming_from_stage7 flag - transitioning back to Stage 4 after error acknowledgment")
                st.rerun()

    with col2:
        st.markdown("**Retry Execution**")
        st.markdown("Clear the error and retry running the script")
        if st.button("🔄 Retry Execution", key="retry_execution", use_container_width=True):
            logger.info(f"User chose to retry execution for step {step_no}")

            # Clear error state to allow retry
            _clear_execution_error_safe(state)

            # Force state update in session state
            st.session_state['state'] = state

            logger.info("Cleared error state for retry")
            st.rerun()

    with col3:
        st.markdown("**View Full Logs**")
        st.markdown("Show complete execution logs and details")
        if st.button("📄 View Full Logs", key="view_full_logs", use_container_width=True):
            # Toggle expanded state for detailed view
            if 'show_full_error_logs' not in st.session_state:
                st.session_state['show_full_error_logs'] = True
            else:
                st.session_state['show_full_error_logs'] = not st.session_state['show_full_error_logs']
            st.rerun()

    # Show full logs if requested
    if st.session_state.get('show_full_error_logs', False):
        with st.expander("📄 Complete Execution Logs", expanded=True):
            st.markdown("**Complete Error Details:**")
            st.json(error_details)

            # Show script content if available
            script_path = error_details.get('script_path', '')
            if script_path and os.path.exists(script_path):
                st.markdown("**Script Content:**")
                try:
                    with open(script_path, 'r', encoding='utf-8') as f:
                        script_content = f.read()
                    st.code(script_content, language="python")
                except Exception as e:
                    st.error(f"Could not read script file: {e}")

    # Add helpful information
    st.markdown("---")
    st.info("💡 **Tip:** Review the error details above to understand what went wrong. You can modify the script in Phase 6 and retry, or acknowledge the error to continue with the next test case step.")


def _has_unacknowledged_error(state):
    """
    Check if there is an unacknowledged execution error.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        bool: True if there is an unacknowledged error, False otherwise
    """
    return (hasattr(state, 'execution_error_occurred') and
            getattr(state, 'execution_error_occurred', False) and
            not getattr(state, 'execution_error_acknowledged', True))


def _set_execution_error_safe(state, error_details):
    """
    Safely set execution error state, handling cases where StateManager might not have the new methods.

    Args:
        state (StateManager): The application state manager instance
        error_details (dict): Error details to store
    """
    import logging
    logger = logging.getLogger("ScriptWeaver.stage7")

    try:
        # Try to use the new method
        if hasattr(state, 'set_execution_error'):
            state.set_execution_error(error_details)
        else:
            # Fallback: manually set the error state
            logger.warning("StateManager missing set_execution_error method, using fallback")

            # Add fields if they don't exist
            if not hasattr(state, 'execution_error_occurred'):
                state.execution_error_occurred = False
            if not hasattr(state, 'execution_error_acknowledged'):
                state.execution_error_acknowledged = False
            if not hasattr(state, 'execution_error_details'):
                state.execution_error_details = {}

            # Set error state manually
            state.execution_error_occurred = True
            state.execution_error_acknowledged = False
            state.execution_error_details = error_details.copy()

            logger.info(f"Fallback: Set execution error for step {error_details.get('step_no', 'Unknown')}")
            logger.error(f"Fallback: Error details - {error_details.get('error_message', 'No message')}")

    except Exception as e:
        logger.error(f"Failed to set execution error state: {e}")
        # Even if we can't set the error state, we should still show the error to the user
        st.error(f"Failed to set error state: {e}")


def _acknowledge_execution_error_safe(state):
    """
    Safely acknowledge execution error, handling cases where StateManager might not have the new methods.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        bool: True if error was acknowledged, False otherwise
    """
    import logging
    logger = logging.getLogger("ScriptWeaver.stage7")

    try:
        if hasattr(state, 'acknowledge_execution_error'):
            return state.acknowledge_execution_error()
        else:
            # Fallback: manually acknowledge error
            logger.warning("StateManager missing acknowledge_execution_error method, using fallback")

            if (hasattr(state, 'execution_error_occurred') and
                state.execution_error_occurred and
                not getattr(state, 'execution_error_acknowledged', True)):

                state.execution_error_acknowledged = True
                logger.info("Fallback: Acknowledged execution error")
                return True
            return False

    except Exception as e:
        logger.error(f"Failed to acknowledge execution error: {e}")
        return False


def _clear_execution_error_safe(state):
    """
    Safely clear execution error state, handling cases where StateManager might not have the new methods.

    Args:
        state (StateManager): The application state manager instance

    Returns:
        bool: True if error was cleared, False otherwise
    """
    import logging
    logger = logging.getLogger("ScriptWeaver.stage7")

    try:
        if hasattr(state, 'clear_execution_error'):
            return state.clear_execution_error()
        else:
            # Fallback: manually clear error
            logger.warning("StateManager missing clear_execution_error method, using fallback")

            if hasattr(state, 'execution_error_occurred') and state.execution_error_occurred:
                state.execution_error_occurred = False
                state.execution_error_acknowledged = False
                if hasattr(state, 'execution_error_details'):
                    state.execution_error_details = {}
                logger.info("Fallback: Cleared execution error state")
                return True
            return False

    except Exception as e:
        logger.error(f"Failed to clear execution error: {e}")
        return False


def _capture_and_store_url_tracking(state, xml_results):
    """
    Capture and store URL tracking information after successful test execution.

    Args:
        state: StateManager instance
        xml_results: Parsed XML results from pytest execution
    """
    try:
        from core.step_data_storage import get_step_data_storage

        # Get the current test case ID and step number
        test_case_id = state.selected_test_case.get('Test Case ID', 'unknown')
        step_no = state.selected_step.get('Step No', 'unknown')

        # Extract URL information from test results
        url_data = {}

        # Check if XML results contain URL information from pytest hooks
        if xml_results and 'test_details' in xml_results:
            for test_detail in xml_results['test_details']:
                properties = test_detail.get('properties', {})

                # Extract URL information captured by pytest hooks
                current_url = properties.get('current_url')
                url_capture_timestamp = properties.get('url_capture_timestamp')

                if current_url:
                    url_data['current_url'] = current_url
                    url_data['url_capture_timestamp'] = url_capture_timestamp

                    # Add to URL history
                    url_history_entry = {
                        'url': current_url,
                        'timestamp': url_capture_timestamp or datetime.now().isoformat(),
                        'step_no': step_no,
                        'action': 'test_execution_complete'
                    }
                    url_data['url_history'] = [url_history_entry]

                    # Set step execution URLs
                    url_data['step_execution_urls'] = {
                        'end_url': current_url,
                        'capture_method': 'pytest_hook'
                    }

                    logger.info(f"Captured URL tracking data for step {step_no}: {current_url}")
                    break

        # If we have URL data, update the step data storage
        if url_data:
            storage = get_step_data_storage()
            success = storage.update_step_url_tracking(test_case_id, step_no, url_data)

            if success:
                logger.info(f"Successfully updated URL tracking for test case {test_case_id}, step {step_no}")

                # Display URL tracking information in the UI
                st.info(f"📍 Current URL captured: {url_data['current_url']}")
            else:
                logger.warning(f"Failed to update URL tracking for test case {test_case_id}, step {step_no}")
        else:
            logger.info(f"No URL tracking data found in test results for step {step_no}")

    except Exception as e:
        logger.error(f"Error in URL tracking capture: {e}")
        # Don't raise the exception to avoid disrupting the main test execution flow
