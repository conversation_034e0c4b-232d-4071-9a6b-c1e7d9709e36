"""
Interactive Element Selector Module

This module provides functionality for interactive UI element selection.
It allows users to open a controlled browser window and manually select
elements on a webpage, capturing their locator information for test script generation.
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from debug_utils import debug

try:
    from .performance_monitor import performance_monitor, BrowserPerformanceMonitor, monitor_interactive_selection
except ImportError:
    # Fallback if performance monitor is not available
    def monitor_interactive_selection(func):
        return func
    performance_monitor = None
    BrowserPerformanceMonitor = None

def setup_interactive_webdriver():
    """
    Set up a Chrome WebDriver for interactive element selection.
    This creates a visible browser window with anti-detection measures.

    Returns:
        WebDriver: A configured WebDriver instance
    """
    chrome_options = Options()

    # Non-headless mode for interactive selection
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-popup-blocking")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--no-sandbox")

    # Performance optimizations
    chrome_options.add_argument("--disable-background-timer-throttling")
    chrome_options.add_argument("--disable-renderer-backgrounding")
    chrome_options.add_argument("--disable-backgrounding-occluded-windows")
    chrome_options.add_argument("--disable-features=TranslateUI")
    chrome_options.add_argument("--disable-ipc-flooding-protection")

    # Enhanced anti-detection measures
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Disable images for faster loading (but keep JavaScript)
    prefs = {
        "profile.managed_default_content_settings.images": 2,
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 0
    }
    chrome_options.add_experimental_option("prefs", prefs)

    # Add a realistic user agent
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    try:
        # Create the driver with ChromeDriverManager
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=chrome_options
        )

        # Additional anti-detection measures using JavaScript
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Set CDP to disable automation flags
        driver.execute_cdp_cmd('Network.setUserAgentOverride', {
            "userAgent": 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

        # Set navigator properties to make detection harder
        driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
            'source': '''
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5]
                });
            '''
        })

        return driver
    except Exception as e:
        print(f"Error setting up interactive WebDriver: {e}")
        return None

def inject_element_selection_script(driver):
    """
    Inject JavaScript to enable interactive element selection.

    Args:
        driver: WebDriver instance

    Returns:
        bool: True if script injection was successful, False otherwise
    """
    try:
        # Inject the element selection script
        script = """
        // Create a global variable to store the selected element
        window.selectedElement = null;
        window.highlightedElement = null;

        // Create a div for displaying element information
        const infoPanel = document.createElement('div');
        infoPanel.id = 'gretah-element-info-panel';
        infoPanel.style.position = 'fixed';
        infoPanel.style.bottom = '0';
        infoPanel.style.left = '0';
        infoPanel.style.right = '0';
        infoPanel.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        infoPanel.style.color = 'white';
        infoPanel.style.padding = '10px';
        infoPanel.style.fontFamily = 'monospace';
        infoPanel.style.fontSize = '14px';
        infoPanel.style.zIndex = '10000';
        infoPanel.style.maxHeight = '150px';
        infoPanel.style.overflow = 'auto';
        infoPanel.innerHTML = '<strong>GRETAH Element Selector</strong>: Hover over elements and click to select. Press ESC to cancel.';
        document.body.appendChild(infoPanel);

        // Create a style for highlighting elements
        const style = document.createElement('style');
        style.innerHTML = `
            .gretah-element-highlight {
                outline: 2px solid #4A6FE3 !important;
                background-color: rgba(74, 111, 227, 0.2) !important;
            }
            .gretah-element-selected {
                outline: 3px solid #00FF00 !important;
                background-color: rgba(0, 255, 0, 0.2) !important;
            }
        `;
        document.head.appendChild(style);

        // Optimized CSS selector generation with caching
        const selectorCache = new WeakMap();
        function getCssSelector(element) {
            // Check cache first
            if (selectorCache.has(element)) {
                return selectorCache.get(element);
            }

            let selector;

            if (element.id) {
                selector = '#' + element.id;
            } else if (element.className && typeof element.className === 'string') {
                const classes = element.className.trim().split(/\\s+/);
                if (classes.length > 0 && classes[0]) {
                    selector = element.tagName.toLowerCase() + '.' + classes[0];
                }
            } else {
                // Try with attributes (optimized order)
                const attrs = ['name', 'type', 'role', 'placeholder'];
                for (let i = 0; i < attrs.length; i++) {
                    const attr = attrs[i];
                    if (element.hasAttribute(attr)) {
                        selector = `${element.tagName.toLowerCase()}[${attr}="${element.getAttribute(attr)}"]`;
                        break;
                    }
                }

                // Fallback to simple tag selector (avoid expensive nth-child calculation)
                if (!selector) {
                    selector = element.tagName.toLowerCase();
                }
            }

            // Cache the result
            selectorCache.set(element, selector);
            return selector;
        }

        // Optimized XPath generation with caching
        const xpathCache = new WeakMap();
        function getXPath(element) {
            // Check cache first
            if (xpathCache.has(element)) {
                return xpathCache.get(element);
            }

            let xpath;

            if (element.id) {
                xpath = `//*[@id="${element.id}"]`;
            } else {
                // Simplified XPath generation - avoid expensive sibling counting
                const parts = [];
                let current = element;

                while (current && current.nodeType === 1 && current.tagName !== 'HTML') {
                    const tagName = current.tagName.toLowerCase();
                    parts.unshift(tagName);
                    current = current.parentNode;
                }

                xpath = '/' + parts.join('/');
            }

            // Cache the result
            xpathCache.set(element, xpath);
            return xpath;
        }

        // Optimized element information with caching
        const elementInfoCache = new WeakMap();
        function getElementInfo(element) {
            if (!element) return {};

            // Check cache first
            if (elementInfoCache.has(element)) {
                return elementInfoCache.get(element);
            }

            const info = {
                tagName: element.tagName.toLowerCase(),
                id: element.id || '',
                name: element.getAttribute('name') || '',
                className: element.className || '',
                type: element.getAttribute('type') || '',
                value: element.value || element.textContent.trim() || '',
                placeholder: element.getAttribute('placeholder') || '',
                href: element.getAttribute('href') || '',
                role: element.getAttribute('role') || '',
                ariaLabel: element.getAttribute('aria-label') || '',
                cssSelector: getCssSelector(element),
                xpath: getXPath(element),
                text: element.textContent.trim()
            };

            // Cache the result
            elementInfoCache.set(element, info);
            return info;
        }

        // Function to update the info panel
        function updateInfoPanel(element) {
            if (!element) {
                infoPanel.innerHTML = '<strong>GRETAH Element Selector</strong>: Hover over elements and click to select. Press ESC to cancel.';
                return;
            }

            const info = getElementInfo(element);
            let html = '<strong>GRETAH Element Selector</strong>: ';

            if (window.selectedElement) {
                html += '<span style="color: #00FF00">Element selected!</span> Press ESC to cancel or click another element.';
            } else {
                html += 'Hover over elements and click to select. Press ESC to cancel.';
            }

            html += '<br><br>';
            html += `<strong>Tag:</strong> ${info.tagName}`;

            if (info.id) html += ` | <strong>ID:</strong> ${info.id}`;
            if (info.name) html += ` | <strong>Name:</strong> ${info.name}`;
            if (info.type) html += ` | <strong>Type:</strong> ${info.type}`;
            if (info.text && info.text.length < 30) html += ` | <strong>Text:</strong> ${info.text}`;

            html += '<br>';
            html += `<strong>CSS:</strong> ${info.cssSelector}`;
            html += '<br>';
            html += `<strong>XPath:</strong> ${info.xpath}`;

            infoPanel.innerHTML = html;
        }

        // Optimized event handling using event delegation and throttling
        let lastHoverTime = 0;
        const HOVER_THROTTLE = 50; // 50ms throttle for hover events

        // Function to handle mouseover with throttling
        function handleMouseOver(event) {
            if (window.selectedElement) return;

            const now = Date.now();
            if (now - lastHoverTime < HOVER_THROTTLE) return;
            lastHoverTime = now;

            if (window.highlightedElement) {
                window.highlightedElement.classList.remove('gretah-element-highlight');
            }

            window.highlightedElement = event.target;
            event.target.classList.add('gretah-element-highlight');
            updateInfoPanel(event.target);

            event.stopPropagation();
        }

        // Function to handle mouseout
        function handleMouseOut(event) {
            if (window.selectedElement) return;

            event.target.classList.remove('gretah-element-highlight');
            updateInfoPanel(null);

            event.stopPropagation();
        }

        // Function to handle click
        function handleClick(event) {
            // Remove previous selection if any
            if (window.selectedElement) {
                window.selectedElement.classList.remove('gretah-element-selected');
            }

            // Remove highlight from previously highlighted element
            if (window.highlightedElement) {
                window.highlightedElement.classList.remove('gretah-element-highlight');
            }

            // Set the new selected element
            window.selectedElement = event.target;
            window.selectedElement.classList.add('gretah-element-selected');

            // Update the info panel
            updateInfoPanel(window.selectedElement);

            // Store the element info in a global variable for Selenium to access
            window.selectedElementInfo = getElementInfo(window.selectedElement);

            event.stopPropagation();
            event.preventDefault();
        }

        // Function to handle keydown (ESC key)
        function handleKeyDown(event) {
            if (event.key === 'Escape') {
                // Remove selection and highlighting
                if (window.selectedElement) {
                    window.selectedElement.classList.remove('gretah-element-selected');
                    window.selectedElement = null;
                }

                if (window.highlightedElement) {
                    window.highlightedElement.classList.remove('gretah-element-highlight');
                    window.highlightedElement = null;
                }

                updateInfoPanel(null);
                window.selectedElementInfo = null;
            }
        }

        // Use event delegation for better performance - attach listeners to document
        document.addEventListener('mouseover', handleMouseOver, true);
        document.addEventListener('mouseout', handleMouseOut, true);
        document.addEventListener('click', handleClick, true);
        document.addEventListener('keydown', handleKeyDown);

        // Expose functions globally for testing and debugging
        window.getCssSelector = getCssSelector;
        window.getXPath = getXPath;
        window.getElementInfo = getElementInfo;
        window.updateInfoPanel = updateInfoPanel;

        // Return true to indicate successful script injection
        return true;
        """

        result = driver.execute_script(script)
        return result is True
    except Exception as e:
        print(f"Error injecting element selection script: {e}")
        return False

def get_selected_element_info(driver):
    """
    Get information about the selected element.

    Args:
        driver: WebDriver instance

    Returns:
        dict: Information about the selected element, or None if no element is selected
    """
    try:
        # Check if an element has been selected
        selected_element_info = driver.execute_script("return window.selectedElementInfo;")
        return selected_element_info
    except Exception as e:
        print(f"Error getting selected element info: {e}")
        return None

@monitor_interactive_selection
def launch_interactive_selector(url, wait_time=5):
    """
    Launch an interactive browser window for element selection.

    Args:
        url (str): URL of the webpage to analyze
        wait_time (int): Time to wait for page to load in seconds

    Returns:
        dict: Information about the selected element, or None if no element was selected
    """
    print(f"Starting interactive element selection for: {url}")

    # Setup driver for interactive mode (non-headless)
    driver = setup_interactive_webdriver()
    if not driver:
        print("Failed to set up interactive WebDriver")
        return None

    try:
        # Navigate to URL
        print(f"Navigating to {url}")
        driver.get(url)

        # Wait for page to load
        WebDriverWait(driver, wait_time).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )

        # Inject the element selection script
        if not inject_element_selection_script(driver):
            print("Failed to inject element selection script")
            return None

        # Initialize browser performance monitoring if available
        browser_monitor = None
        if BrowserPerformanceMonitor:
            browser_monitor = BrowserPerformanceMonitor(driver)
            browser_monitor.inject_performance_monitoring_script()

        print("Interactive element selection mode activated")
        print("Please select an element in the browser window")

        # Optimized polling with exponential backoff
        selected_element_info = None
        max_wait_time = 300  # 5 minutes timeout
        start_time = time.time()
        poll_interval = 0.1  # Start with faster polling
        max_poll_interval = 2.0  # Maximum polling interval

        while time.time() - start_time < max_wait_time:
            # Check if an element has been selected
            selected_element_info = get_selected_element_info(driver)
            if selected_element_info:
                print("Element selected!")
                # Log browser performance metrics if available
                if browser_monitor:
                    metrics = browser_monitor.get_browser_metrics()
                    if metrics:
                        print(f"Browser performance - Events: {metrics.get('eventCounts', {})}")
                break

            # Exponential backoff for polling interval
            time.sleep(poll_interval)
            poll_interval = min(poll_interval * 1.2, max_poll_interval)

        return selected_element_info

    except Exception as e:
        print(f"Error during interactive element selection: {e}")
        return None

    finally:
        # Keep the browser open for a moment so the user can see the selection
        time.sleep(2)

        try:
            driver.quit()
        except:
            pass

@monitor_interactive_selection
def select_element_interactively(url, browser=None, headless=False):
    """
    Open a browser window for interactive element selection.

    This function allows users to select UI elements on a webpage interactively.
    It opens a browser window, navigates to the specified URL, and waits for the
    user to select an element. The selected element's information is returned.

    Args:
        url (str): URL of the webpage to select elements from
        browser (WebDriver, optional): Selenium WebDriver instance. If None, a new one will be created
        headless (bool, optional): Whether to run the browser in headless mode. Defaults to False

    Returns:
        dict: The selected element information, or None if no element was selected
    """
    debug(f"Opening interactive element selector for {url}",
          stage="interactive_selector", operation="selector_opened",
          context={"url": url, "browser_provided": browser is not None, "headless": headless})

    # If headless is True, override it to False since interactive selection requires a visible browser
    if headless:
        debug("Headless mode not supported for interactive element selection. Using visible browser.",
              stage="interactive_selector", operation="headless_override",
              context={"url": url})
        headless = False

    # If browser is provided, use it; otherwise, create a new one
    if browser:
        debug("Using provided browser instance",
              stage="interactive_selector", operation="browser_reuse",
              context={"url": url})
        # Inject the element selection script into the provided browser
        if not inject_element_selection_script(browser):
            debug("Failed to inject element selection script",
                  stage="interactive_selector", operation="script_injection_error",
                  context={"url": url})
            return None

        # Optimized polling with exponential backoff
        selected_element_info = None
        max_wait_time = 300  # 5 minutes timeout
        start_time = time.time()
        poll_interval = 0.1  # Start with faster polling
        max_poll_interval = 2.0  # Maximum polling interval

        while time.time() - start_time < max_wait_time:
            # Check if an element has been selected
            selected_element_info = get_selected_element_info(browser)
            if selected_element_info:
                debug("Element selected!",
                      stage="interactive_selector", operation="element_selected",
                      context={"url": url, "element_info": selected_element_info})
                return selected_element_info

            # Exponential backoff for polling interval
            time.sleep(poll_interval)
            poll_interval = min(poll_interval * 1.2, max_poll_interval)

        debug("No element selected within timeout period",
              stage="interactive_selector", operation="selection_timeout",
              context={"url": url, "timeout_seconds": max_wait_time})
        return None
    else:
        # Launch the interactive selector
        return launch_interactive_selector(url)
