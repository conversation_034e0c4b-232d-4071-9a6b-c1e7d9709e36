"""
Stage 4: UI Element Detection and Test Case Step Selection

This module handles UI element detection, interactive element selection,
and element matching for test case steps.
Maintains the StateManager pattern and follows the established architectural patterns.

Phase 3c Enhancement: Standardized logging with centralized infrastructure
"""

import os
import logging
import streamlit as st
import time
from datetime import datetime

# Enhanced logging configuration using centralized infrastructure
from core.logging_config import get_stage_logger
logger = get_stage_logger("stage4")

# Import helper functions from other modules
from core.element_detection import filter_qa_relevant_elements, select_element_interactively
from core.element_matching import match_elements_with_ai
from core.locator_resolver import resolve_locator_conflicts
from helpers_pure import analyze_step_for_test_data
from state_manager import StateStage
from debug_utils import debug
from ui_components.locator_resolution_display import (
    render_locator_resolution_display,
    render_locator_resolution_summary
)


def stage4_ui_detection_and_matching(state):
    """Phase 4: UI Element Detection and Test Case Step Selection."""
    st.markdown("<h2 class='stage-header'>Phase 4: UI Element Detection</h2>", unsafe_allow_html=True)

    # Check if we have a stage progression message to display
    if 'stage_progression_message' in st.session_state:
        st.success(st.session_state['stage_progression_message'])
        # Remove the message so it doesn't show up again
        del st.session_state['stage_progression_message']

    # Check if we're coming from Stage 7 (automatic advancement)
    if 'coming_from_stage7' in st.session_state:
        # Get information about the advancement
        if 'force_refresh_after_advance' in st.session_state:
            from_step = st.session_state['force_refresh_after_advance'].get('from_step')
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')

            # Display a compact banner
            st.success(f"✅ Advanced from Step {from_step} to Step {target_step}")

        # Clear the flag so it doesn't show up again
        del st.session_state['coming_from_stage7']

    # Check if we need to force a refresh after automatic advancement
    if 'force_refresh_after_advance' in st.session_state:
        # Get the timestamp to see if this is a recent advancement
        advance_time = st.session_state['force_refresh_after_advance'].get('timestamp')
        from datetime import datetime, timedelta
        now = datetime.now()

        # If the advancement was within the last 5 seconds, force a refresh
        if advance_time and (now - datetime.strptime(advance_time, "%H:%M:%S.%f")) < timedelta(seconds=5):
            # Clear the flag so we don't keep refreshing
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')
            logger.info(f"Forcing refresh to ensure UI shows step {target_step}")
            del st.session_state['force_refresh_after_advance']

            # Force a rerun to refresh the UI
            time.sleep(0.5)
            st.rerun()

    # Check if prerequisites are met - JSON-ONLY mode
    try:
        # Load step data from JSON storage (single source of truth)
        step_table_json = state.get_effective_step_table()
        logger.info(f"✅ Loaded {len(step_table_json)} steps from JSON storage")
    except ValueError as e:
        st.error("❌ **Step Data Not Available**")
        st.error(f"Error: {e}")

        with st.expander("🔧 How to Fix This", expanded=True):
            st.markdown("""
            **This error occurs because no step data was found in persistent storage.**

            **To resolve this issue:**
            1. 📋 Go back to **Stage 3** (Test Case Analysis)
            2. 🔄 Select your test case and click **"Convert Test Case"**
            3. ✅ Wait for the conversion to complete
            4. 🔄 Return to this stage

            **Why this happens:**
            - The test case hasn't been converted to automation format yet
            - The JSON step data file is missing or corrupted
            - You may have selected a different test case
            """)

            if st.button("🔄 Go to Stage 3", key="goto_stage3_btn"):
                state.advance_to(StateStage.STAGE3_CONVERT, "User requested to fix missing step data")
                st.rerun()
        return

    # Step selection section in a collapsible section
    with st.expander("Step Selection", expanded=True):
        # Add a reset button to start over with step 1
        reset_cols = st.columns([3, 1])
        with reset_cols[0]:
            if st.button("Reset to Step 1", key="reset_step_btn", help="Reset to the first step of the test case"):
                # Check if we have progress to confirm reset
                has_progress = (hasattr(state, 'current_step_index') and state.current_step_index > 0) or state.all_steps_done

                if has_progress:
                    st.warning("⚠️ Resetting will clear all progress.")
                    confirm_reset = st.button("Confirm Reset", key="confirm_reset_step")

                    if not confirm_reset:
                        st.info("Reset cancelled.")
                        return

                # Use the state manager's update method to reset step progress
                state.update_step_progress(
                    current_step_index=0,
                    all_steps_done=False,
                    step_ready_for_script=False,
                    script_just_generated=False
                )

                # Reset step-specific state
                state.reset_step_state(confirm=True, reason="User requested reset to Step 1")

                st.success("✅ Reset complete.")
                st.rerun()

    # Get total steps if not already set (use effective step count for hybrid editing)
    if state.total_steps == 0:
        if hasattr(state, 'get_effective_total_steps'):
            state.total_steps = state.get_effective_total_steps()
        elif state.step_table_json:
            state.total_steps = len(state.step_table_json)

    # Show completion message if all steps are processed
    if state.all_steps_done:
        st.success("✅ All test case steps have been processed!")
        return

    # Show "Proceed to next step" button if the current step is ready for script and there are more steps
    if state.step_ready_for_script:
        # Get the next step information
        next_step_index = state.current_step_index + 1
        if next_step_index < state.total_steps:
            next_step = state.step_table_json[next_step_index]
            next_step_no = next_step.get('step_no', 'N/A')
            next_step_action = next_step.get('action', 'N/A')

            # Add section for the next step button
            st.markdown("---")
            _show_next_step_banner(state, next_step_no, next_step_action)
            _create_proceed_button(state, next_step_no, next_step_action, "stage4")
            return

    # Enhanced logging for step table data analysis
    debug("Stage 4: Using JSON-only step data",
          stage="stage4",
          operation="step_data_analysis",
          context={
              'step_count': len(step_table_json),
              'data_source': 'json_storage'
          })

    if step_table_json:
        first_step = step_table_json[0]
        debug("Stage 4: First step analysis",
              stage="stage4",
              operation="step_data_analysis",
              context={
                  'step_no': first_step.get('step_no'),
                  'action': first_step.get('action'),
                  'is_manual': first_step.get('_is_manual', False)
              })

        if len(step_table_json) > 1:
            second_step = step_table_json[1]
            debug("Stage 4: Second step analysis",
                  stage="stage4",
                  operation="step_data_analysis",
                  context={
                      'step_no': second_step.get('step_no'),
                      'action': second_step.get('action'),
                      'is_manual': second_step.get('_is_manual', False)
                  })

    if not isinstance(step_table_json, list):
        st.error("❌ Step table data is not in the correct format")
        st.error("Please re-convert the test case in Stage 3")
        return

    # Create step selection dropdown from the step table
    step_options = []
    for step in step_table_json:
        if isinstance(step, dict):
            step_no = step.get('step_no', 'N/A')
            action = step.get('action', 'N/A')
            # Add visual indicator for manual steps
            step_indicator = "✏️" if step.get('_is_manual', False) else "🤖"
            step_options.append(f"{step_indicator} Step {step_no} - {action}")

    if not step_options:
        st.warning("No steps found in the step table")
        return

    # Initialize to the first step if needed
    if state.current_step_index < 0 or state.current_step_index >= len(step_options):
        old_index = state.current_step_index
        state.current_step_index = 0
        debug("Initializing current_step_index to 0 (was out of bounds)",
              stage="stage4",
              operation="step_index_initialization",
              context={
                  'old_index': old_index,
                  'new_index': 0,
                  'total_steps': len(step_options)
              })

    # Enhanced logging for current step index
    debug("Current step index status",
          stage="stage4",
          operation="step_index_tracking",
          context={
              'current_index': state.current_step_index,
              'total_steps': len(step_options)
          })

    # Get the current step option
    try:
        # Check if we're coming from Stage 7 with a specific step
        if 'coming_from_stage7' in st.session_state and 'force_refresh_after_advance' in st.session_state:
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')
            logger.info(f"Coming from Stage 7, looking for step {target_step}")

            # Find the step option that matches the target step
            matching_options = [opt for opt in step_options if f"Step {target_step}" in opt]
            if matching_options:
                # Find the index of the matching option
                target_index = step_options.index(matching_options[0])

                # If the current index doesn't match the target, update it
                if state.current_step_index != target_index:
                    logger.info(f"Updating current_step_index from {state.current_step_index} to {target_index} to match target step {target_step}")
                    state.current_step_index = target_index

        # Get the current step option based on the (possibly updated) index
        current_step_option = step_options[state.current_step_index]
        logger.info(f"Selected step option: {current_step_option}")
    except IndexError:
        # Handle index error gracefully
        logger.error(f"Index error: current_step_index={state.current_step_index}, len(step_options)={len(step_options)}")
        st.error(f"Error: Step index {state.current_step_index} is out of range (0-{len(step_options)-1})")
        # Reset to a valid index
        state.current_step_index = 0
        current_step_option = step_options[0]
        logger.info(f"Reset to step option: {current_step_option}")

    # Create step selection UI in a collapsible section
    with st.expander("Step Navigation", expanded=True):
        # Create columns for step selection and navigation
        step_col1, step_col2 = st.columns([3, 1])

        with step_col1:
            # Allow direct selection of steps with a selectbox
            selected_index = step_options.index(current_step_option)
            selected_step_option = st.selectbox(
                "Select Test Case Step",
                step_options,
                index=selected_index,
                help="Select a specific test case step to process"
            )

            # If user selected a different step, update the current step index
            if selected_step_option != current_step_option:
                new_index = step_options.index(selected_step_option)

                # Show a warning about changing steps
                st.warning("⚠️ Switching steps will reset current progress.")

                # Add a confirmation button
                if st.button("Confirm Step Change", key="confirm_step_change"):
                    # Check if the current step has any progress
                    has_step_progress = (
                        (hasattr(state, 'step_elements') and state.step_elements) or
                        (hasattr(state, 'step_matches') and state.step_matches) or
                        (hasattr(state, 'test_data') and state.test_data) or
                        (hasattr(state, 'test_data_skipped') and state.test_data_skipped) or
                        (hasattr(state, 'step_ready_for_script') and state.step_ready_for_script) or
                        (hasattr(state, 'script_just_generated') and state.script_just_generated)
                    )

                    # Log the step change with progress information
                    logger.info(f"Changing step from {state.current_step_index} to {new_index} (has_progress={has_step_progress})")

                    # Use the state manager's update method to update step index
                    state.update_step_progress(current_step_index=new_index)

                    # Reset step-specific state
                    state.reset_step_state(confirm=True, reason=f"User changed from step {state.current_step_index + 1} to step {new_index + 1}")

                    st.success(f"✅ Changed to Step {new_index + 1}")
                    st.rerun()

                # If not confirmed, revert to the current step
                selected_step_option = current_step_option

        with step_col2:
            # Add step completion tracking
            if hasattr(state, 'completed_steps') and isinstance(state.completed_steps, list):
                completed_count = len(state.completed_steps)
                st.metric("Steps Completed", f"{completed_count} of {state.total_steps}")
            else:
                # Initialize completed_steps if not present
                state.completed_steps = []
                st.metric("Steps Completed", f"0 of {state.total_steps}")

    # Set the selected step option for the rest of the code to use
    # This will be the current step unless the user confirmed a change

    if selected_step_option != "Select a step...":
        # Extract step number more robustly to handle hybrid editing formats
        step_no_raw = selected_step_option.split(" - ")[0]
        # Remove emoji indicators and "Step " prefix
        step_no = step_no_raw.replace("✏️", "").replace("🤖", "").replace("Step ", "").strip()
        logger.info(f"Processing step_no: {step_no} from selected_step_option: {selected_step_option}")

        # Find the selected step in the step table (from effective step table)
        logger.info(f"=== Stage 4: Finding step table entry for step {step_no} ===")
        logger.info(f"Stage 4: Searching in step_table_json with {len(step_table_json)} steps")

        selected_step_table_entry = next(
            (step for step in step_table_json if str(step.get('step_no')) == step_no),
            None
        )

        if selected_step_table_entry:
            logger.info(f"Stage 4: ✓ Found step table entry for step {step_no}")
            logger.info(f"  - action: '{selected_step_table_entry.get('action')}'")
            logger.info(f"  - locator: '{selected_step_table_entry.get('locator')}'")
            logger.info(f"  - expected_result: '{selected_step_table_entry.get('expected_result')}'")
            logger.info(f"  - is_manual: {selected_step_table_entry.get('_is_manual', False)}")
            logger.info(f"  - is_ai_generated: {selected_step_table_entry.get('_is_ai_generated', False)}")
        else:
            logger.error(f"Stage 4: ✗ Could not find step table entry for step {step_no}")
            logger.error("Stage 4: Available steps in step_table_json:")
            for i, step in enumerate(step_table_json):
                logger.error(f"  [{i}] step_no: '{step.get('step_no')}' - action: '{step.get('action')}' - is_manual: {step.get('_is_manual', False)}")

        # For hybrid editing, we need to handle the case where the step might be manually added
        # and not exist in the original test case
        original_steps = state.selected_test_case.get('Steps', [])
        selected_original_step = next(
            (step for step in original_steps if str(step.get('Step No')) == step_no),
            None
        )

        # If no original step found but we have a step table entry, create a synthetic original step
        # This handles manually added steps in hybrid editing
        if not selected_original_step and selected_step_table_entry:
            logger.info(f"No original step found for step {step_no}, creating synthetic step from step table entry")
            selected_original_step = {
                'Step No': step_no,
                'Test Steps': selected_step_table_entry.get('action', 'Manual step'),
                'Expected Result': selected_step_table_entry.get('expected_result', 'Step completed successfully'),
                '_is_synthetic': True,  # Mark as synthetic for debugging
                '_source': 'hybrid_editing'  # Indicate source
            }
            logger.info(f"Created synthetic original step for step {step_no}: {selected_original_step.get('Test Steps')}")
        elif selected_original_step:
            logger.info(f"Found original step for step {step_no}: {selected_original_step.get('Test Steps')}")
        else:
            logger.error(f"Could not find or create original step for step {step_no}")
            # Log all available original steps for debugging
            for step in original_steps:
                logger.info(f"Available original step: {step.get('Step No')} - {step.get('Test Steps')}")

        if selected_step_table_entry and selected_original_step:
            # Store both versions in state manager
            logger.info(f"=== Stage 4: Storing step data in state manager ===")
            logger.info(f"Stage 4: About to store data for step {step_no}")

            # Store the step table entry (automation-ready format)
            old_step_table_entry = getattr(state, 'selected_step_table_entry', None)
            state.selected_step_table_entry = selected_step_table_entry
            logger.info(f"Stage 4: ✓ Stored selected_step_table_entry")
            logger.info(f"  - action: '{selected_step_table_entry.get('action')}'")
            logger.info(f"  - locator: '{selected_step_table_entry.get('locator')}'")
            logger.info(f"  - expected_result: '{selected_step_table_entry.get('expected_result')}'")
            logger.info(f"  - is_manual: {selected_step_table_entry.get('_is_manual', False)}")
            logger.info(f"  - is_ai_generated: {selected_step_table_entry.get('_is_ai_generated', False)}")

            # Store the original step (original format)
            old_step = getattr(state, 'selected_step', None)
            state.selected_step = selected_original_step
            logger.info(f"Stage 4: ✓ Stored selected_step")
            logger.info(f"  - Test Steps: '{selected_original_step.get('Test Steps')}'")
            logger.info(f"  - Expected Result: '{selected_original_step.get('Expected Result')}'")
            logger.info(f"  - Is synthetic: {selected_original_step.get('_is_synthetic', False)}")
            logger.info(f"  - Source: {selected_original_step.get('_source', 'original')}")

            # Verify data consistency for hybrid editing
            step_table_action = selected_step_table_entry.get('action', '')
            original_action = selected_original_step.get('Test Steps', '')
            actions_match = step_table_action.lower() == original_action.lower()

            logger.info(f"Stage 4: Data consistency check:")
            logger.info(f"  - Step table action: '{step_table_action}'")
            logger.info(f"  - Original action: '{original_action}'")
            logger.info(f"  - Actions match: {actions_match}")

            if not actions_match:
                logger.warning(f"Stage 4: ⚠️ Action mismatch detected - this indicates hybrid editing is active")
                logger.warning(f"Stage 4: Step table entry should be used as authoritative source for script generation")

            logger.info(f"=== Stage 4: Step data storage completed ===")

            # Validate step data consistency for debugging
            if hasattr(state, 'validate_step_data_consistency'):
                validation_results = state.validate_step_data_consistency("Stage 4")
                if not validation_results["data_sources_consistent"]:
                    logger.error(f"Stage 4: Data consistency issues detected!")
                    for issue in validation_results["issues"]:
                        logger.error(f"  - {issue}")

            # Show context from previous steps if available
            _display_previous_step_context(state, selected_original_step)

            # Display step details in a collapsible section
            with st.expander("Step Details", expanded=True):
                # Create tabs for different views of the step
                step_tab1, step_tab2 = st.tabs(["Automation-Ready Format", "Original Format"])

                with step_tab1:
                    # Display the automation-ready step details
                    col1, col2 = st.columns(2)
                    with col1:
                        st.markdown(f"**Step Number:** {selected_step_table_entry.get('step_no')}")
                        st.markdown(f"**Action:** {selected_step_table_entry.get('action')}")
                        st.markdown(f"**Locator Strategy:** {selected_step_table_entry.get('locator_strategy')}")
                        st.markdown(f"**Locator:** {selected_step_table_entry.get('locator')}")
                    with col2:
                        st.markdown(f"**Test Data Parameter:** {selected_step_table_entry.get('test_data_param')}")
                        st.markdown(f"**Expected Result:** {selected_step_table_entry.get('expected_result')}")
                        st.markdown(f"**Assertion Type:** {selected_step_table_entry.get('assertion_type')}")
                        st.markdown(f"**Timeout:** {selected_step_table_entry.get('timeout')} seconds")

                with step_tab2:
                    # Display the original step details
                    st.markdown(f"**Step Number:** {selected_original_step.get('Step No')}")
                    st.markdown(f"**Test Steps:** {selected_original_step.get('Test Steps')}")
                    st.markdown(f"**Expected Result:** {selected_original_step.get('Expected Result')}")

            # Check if this step should trigger automatic stage progression
            progression_triggered = _check_automatic_stage_progression(state, selected_step_table_entry, selected_original_step)

            # If automatic progression was triggered, return early to avoid rendering UI elements
            if progression_triggered:
                logger.info(f"Automatic stage progression triggered for step {selected_original_step.get('Step No')} - stopping UI rendering")
                return

            # Stage 4b: UI Element Detection
            _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step)

            # Stage 4c: Element Matching
            _handle_element_matching(state, selected_step_table_entry, selected_original_step)


def _check_automatic_stage_progression(state, selected_step_table_entry, selected_original_step):
    """
    Check if the selected step should trigger automatic stage progression.

    This function analyzes the step to determine if it can be processed automatically
    without user interaction, and if so, advances to the appropriate next stage.

    Args:
        state (StateManager): The application state manager instance
        selected_step_table_entry (dict): The automation-ready step table entry
        selected_original_step (dict): The original test case step
    """
    try:
        # Check if this is a navigation step that doesn't require UI elements
        locator_strategy = selected_step_table_entry.get('locator_strategy', '')
        is_navigation_step = locator_strategy in ["", "none", "n/a", "url"]

        # Check if we already have element matches for this step (from previous processing)
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))
        has_existing_matches = (
            hasattr(state, 'element_matches') and
            state.element_matches and
            test_case_id in state.element_matches and
            step_no in state.element_matches[test_case_id]
        )

        # Check if we have manually selected elements for this step
        has_manual_selection = (
            has_existing_matches and
            any(match.get('manually_selected', False)
                for match in state.element_matches[test_case_id][step_no])
        )

        # Enhanced logging for debugging
        logger.info(f"Automatic progression check for step {step_no}:")
        logger.info(f"  - locator_strategy: '{locator_strategy}'")
        logger.info(f"  - is_navigation_step: {is_navigation_step}")
        logger.info(f"  - test_case_id: '{test_case_id}'")
        logger.info(f"  - has_existing_matches: {has_existing_matches}")
        logger.info(f"  - has_manual_selection: {has_manual_selection}")
        logger.info(f"  - current_stage: {state.current_stage}")

        # Debug element matches structure
        if hasattr(state, 'element_matches') and state.element_matches:
            logger.info(f"  - element_matches keys: {list(state.element_matches.keys())}")
            if test_case_id in state.element_matches:
                logger.info(f"  - steps in element_matches[{test_case_id}]: {list(state.element_matches[test_case_id].keys())}")
        else:
            logger.info(f"  - element_matches: None or empty")

        # If this is a navigation step or we already have matches, process automatically
        if is_navigation_step or has_existing_matches:
            logger.info(f"Step {step_no} qualifies for automatic progression - analyzing test data requirements")

            # Analyze if the step requires test data
            try:
                test_data_analysis = analyze_step_for_test_data(
                    selected_step_table_entry,
                    selected_original_step.get('Test Steps')
                )
                logger.info(f"Test data analysis for step {step_no}: requires_test_data={test_data_analysis.get('requires_test_data', 'unknown')}")
            except Exception as e:
                logger.error(f"Error in test data analysis for step {step_no}: {e}")
                # Fallback to requiring test data to be safe
                test_data_analysis = {
                    "requires_test_data": True,
                    "reason": f"Error in analysis: {e}",
                    "data_types": []
                }

            # Create or update element matches structure
            if not hasattr(state, 'element_matches'):
                state.element_matches = {}

            if test_case_id not in state.element_matches:
                state.element_matches[test_case_id] = {}

            # For navigation steps, create empty matches if they don't exist
            if is_navigation_step and step_no not in state.element_matches[test_case_id]:
                state.element_matches[test_case_id][step_no] = []
                logger.info(f"Created empty element matches for navigation step {step_no}")

            # Update state with analysis results
            state.step_matches = state.element_matches
            state.llm_step_analysis = {
                "requires_ui_element": not is_navigation_step,
                "reason": "Navigation step - no UI elements needed" if is_navigation_step else "UI elements already matched",
                "matches": state.element_matches[test_case_id].get(step_no, []),
                "requires_test_data": test_data_analysis["requires_test_data"],
                "test_data_reason": test_data_analysis["reason"],
                "data_types": test_data_analysis["data_types"]
            }

            # Store test data analysis separately for easier access
            state.test_data_analysis = test_data_analysis

            # Determine next stage based on test data requirements
            if not test_data_analysis["requires_test_data"]:
                # No test data needed - advance to Stage 6
                state.test_data_skipped = True
                state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists

                logger.info(f"Step {step_no} requires no test data - attempting to advance to Stage 6")
                logger.info(f"Current stage before advance: {state.current_stage}")

                if state.current_stage == StateStage.STAGE4_DETECT:
                    advance_success = state.advance_to(StateStage.STAGE6_GENERATE, f"Step {step_no} processed automatically, no test data needed - advancing to Stage 6")
                    logger.info(f"Stage advance to Stage 6 success: {advance_success}")
                    logger.info(f"Current stage after advance: {state.current_stage}")

                    if advance_success:
                        # Force state update in session state
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = f"✅ Step {step_no} processed automatically. Proceeding to Stage 6 (no test data needed)."

                        logger.info(f"Calling st.rerun() for stage transition to Stage 6")
                        # Call st.rerun() to immediately refresh the UI
                        st.rerun()
                        return True
                    else:
                        logger.error(f"Failed to advance to Stage 6 for step {step_no}")
                else:
                    logger.warning(f"Not in Stage 4 - current stage is {state.current_stage}, cannot advance to Stage 6")
            else:
                # Test data needed - advance to Stage 5
                logger.info(f"Step {step_no} requires test data - attempting to advance to Stage 5")
                logger.info(f"Current stage before advance: {state.current_stage}")

                if state.current_stage == StateStage.STAGE4_DETECT:
                    advance_success = state.advance_to(StateStage.STAGE5_DATA, f"Step {step_no} processed automatically - advancing to Stage 5 for test data")
                    logger.info(f"Stage advance to Stage 5 success: {advance_success}")
                    logger.info(f"Current stage after advance: {state.current_stage}")

                    if advance_success:
                        # Force state update in session state
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = f"✅ Step {step_no} processed automatically. Proceeding to Stage 5 for test data configuration."

                        logger.info(f"Calling st.rerun() for stage transition to Stage 5")
                        # Call st.rerun() to immediately refresh the UI
                        st.rerun()
                        return True
                    else:
                        logger.error(f"Failed to advance to Stage 5 for step {step_no}")
                else:
                    logger.warning(f"Not in Stage 4 - current stage is {state.current_stage}, cannot advance to Stage 5")

        # If we reach here, no automatic progression was triggered
        logger.info(f"No automatic progression triggered for step {step_no} - user interaction required")
        return False

    except Exception as e:
        logger.error(f"Error in automatic stage progression check: {e}")
        return False


def _show_next_step_banner(state, next_step_no, next_step_action):
    """
    Display a banner indicating the next step is ready.

    Shows progress information in the sidebar instead of the main content area.

    Args:
        state (StateManager): The application state manager instance
        next_step_no (str): The step number of the next step
        next_step_action (str): The action description of the next step
    """
    # Display progress information in the sidebar
    with st.sidebar:
        st.success(f"✅ Step {state.selected_step.get('Step No')} completed")
        st.info(f"Ready for Step {next_step_no}")

    # Add a small indicator in the main content area
    st.success("✅ Current step ready for next step")


def _create_proceed_button(state, next_step_no, next_step_action, stage_key):
    """
    Create a button to proceed to the next step.

    Creates a button that advances to the next step in the test case workflow
    when clicked, with simplified UI and clear manual progression.

    Args:
        state (StateManager): The application state manager instance
        next_step_no (str): The step number of the next step
        next_step_action (str): The action description of the next step
        stage_key (str): A unique identifier for the button based on the current stage
    """
    # Create a simple button with clear label
    button_label = f"Proceed to Step {next_step_no}"

    if st.button(button_label, key=f"proceed_to_next_step_{stage_key}", use_container_width=True):
        with st.spinner(f"Advancing to Step {next_step_no}..."):
            # Log the current state before advancing
            logger.info(f"Button clicked: Proceeding to Step {next_step_no}")

            # Store the current step index for comparison
            old_step_index = state.current_step_index

            # Reset the flags
            if state.step_ready_for_script:
                state.step_ready_for_script = False
                logger.info("State change: step_ready_for_script = False")

            # Store the current timestamp for debugging
            proceed_timestamp = datetime.now().strftime("%H:%M:%S.%f")

            # Get the current step number
            current_step_index = state.current_step_index
            next_step_index = current_step_index + 1

            # Check if the next step index is valid
            if next_step_index < state.total_steps:
                # Add the current step to the completed steps list
                if not hasattr(state, 'completed_steps'):
                    state.completed_steps = []

                # Get the current step number
                current_step_no = state.step_table_json[current_step_index].get('step_no')

                # Add to completed steps if not already there
                if current_step_no not in state.completed_steps:
                    state.completed_steps.append(str(current_step_no))

                # Update step progress
                state.update_step_progress(
                    current_step_index=next_step_index,
                    step_ready_for_script=False,
                    script_just_generated=False
                )

                # Reset step-specific state
                state.reset_step_state(confirm=True, reason=f"User proceeded from step {current_step_index + 1} to step {next_step_index + 1}")

                # Add a message to display on the next page
                st.session_state['stage_progression_message'] = f"✅ Proceeded to Step {next_step_no}"

                # Store information about the advancement for UI refresh
                st.session_state['force_refresh_after_advance'] = {
                    'timestamp': proceed_timestamp,
                    'from_step': current_step_no,
                    'target_step': next_step_no
                }

                # Rerun the app to show the next step
                st.rerun()
            else:
                # All steps are done
                state.all_steps_done = True
                st.success("✅ All test case steps have been processed!")
                st.rerun()


def _ensure_step_table_analysis(state):
    """
    Ensure that step_table_analysis exists and is valid.
    Re-generate it if missing or None.

    Args:
        state: StateManager instance

    Returns:
        dict: Step table analysis results
    """
    # Check if we have valid step table analysis
    if (hasattr(state, 'step_table_analysis') and
        state.step_table_analysis and
        isinstance(state.step_table_analysis, dict)):
        return state.step_table_analysis

    # Re-generate analysis if missing or invalid
    logger.info("Step table analysis missing or invalid, regenerating...")

    try:
        # Import the analysis function
        from core.ai_helpers import analyze_step_table

        # Get the effective step table
        effective_step_table = state.get_effective_step_table() if hasattr(state, 'get_effective_step_table') else state.step_table_json

        if effective_step_table and isinstance(effective_step_table, list):
            # Create markdown representation for analysis
            markdown_table = state.step_table_markdown if hasattr(state, 'step_table_markdown') and state.step_table_markdown else ""

            # Analyze the step table
            step_table_analysis = analyze_step_table((markdown_table, effective_step_table))

            # Store the analysis in state
            state.step_table_analysis = step_table_analysis

            logger.info(f"Regenerated step table analysis: requires_ui_elements={step_table_analysis.get('requires_ui_elements', True)}")
            return step_table_analysis
        else:
            logger.warning("No valid step table found for analysis")

    except Exception as e:
        logger.error(f"Error regenerating step table analysis: {e}")

    # Return default analysis if regeneration fails
    default_analysis = {
        "requires_ui_elements": True,
        "reason": "Default analysis - UI element detection recommended for safety",
        "actions": [],
        "locator_strategies": []
    }

    state.step_table_analysis = default_analysis
    return default_analysis


def _is_navigation_step_or_no_ui_required(state, selected_step_table_entry):
    """
    Check if this is a navigation step or doesn't require UI elements.

    Args:
        state: StateManager instance
        selected_step_table_entry: The selected step table entry

    Returns:
        bool: True if no UI elements are required
    """
    # Ensure we have valid step table analysis
    step_table_analysis = _ensure_step_table_analysis(state)

    # Check if analysis says no UI elements required
    analysis_says_no_ui = not step_table_analysis.get("requires_ui_elements", True)

    # Check if step has navigation-type locator strategy
    locator_strategy = selected_step_table_entry.get('locator_strategy', '')
    is_navigation_locator = locator_strategy in ["", "none", "n/a", "url"]

    return analysis_says_no_ui or is_navigation_locator


def _requires_ui_elements_but_not_detected(state, selected_step_table_entry):
    """
    Check if UI elements are required but not yet detected.

    Args:
        state: StateManager instance
        selected_step_table_entry: The selected step table entry

    Returns:
        bool: True if UI elements are required but not detected
    """
    # Ensure we have valid step table analysis
    step_table_analysis = _ensure_step_table_analysis(state)

    # Check if analysis says UI elements are required
    analysis_requires_ui = step_table_analysis.get("requires_ui_elements", True)

    # Check if step has non-navigation locator strategy
    locator_strategy = selected_step_table_entry.get('locator_strategy', '')
    step_requires_ui = locator_strategy not in ["", "none", "n/a", "url"]

    # Check if elements have been detected
    elements_detected = hasattr(state, 'detected_elements') and state.detected_elements

    return analysis_requires_ui and step_requires_ui and not elements_detected


def _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step):
    """Handle UI element detection for the selected test case step."""
    with st.expander("UI Element Detection", expanded=True):
        # Ensure we have valid step table analysis
        step_table_analysis = _ensure_step_table_analysis(state)

        # Get analysis results with safe defaults
        requires_ui_elements = step_table_analysis.get("requires_ui_elements", True)
        ui_element_reason = step_table_analysis.get("reason", "UI element detection is needed for proper automation.")

        # Also check the specific step's requirements
        step_requires_ui_elements = selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"]

        # Display message about UI element detection requirement
        if requires_ui_elements and step_requires_ui_elements:
            st.info(f"🔍 {ui_element_reason}")

            # Show the Interactive Selection button
            select_interactive_button = st.button(
                "👆 Select Element Interactively",
                key="select_element_interactive_btn",
                help="Open a browser window to manually select UI elements for this step",
                use_container_width=True
            )

            if select_interactive_button:
                _handle_interactive_element_selection(state, selected_step_table_entry, selected_original_step)
        else:
            # If UI element detection is not needed, show a message and set empty elements
            st.success(f"✓ Element detection not needed: {ui_element_reason}")

            # Create empty elements for the workflow to continue
            if not hasattr(state, 'detected_elements') or not state.detected_elements:
                state.detected_elements = []
                state.qa_relevant_elements = []


def _handle_interactive_element_selection(state, selected_step_table_entry, selected_original_step):
    """Handle interactive element selection for the selected test case step."""
    with st.spinner("Opening browser for interactive element selection. Please select an element in the browser window..."):
        try:
            # Get locator strategy and value from the step table entry
            locator_strategy = None
            locator_value = None

            if selected_step_table_entry:
                locator_strategy = selected_step_table_entry.get('locator_strategy')
                locator_value = selected_step_table_entry.get('locator')

            # Launch interactive element selector
            website_url = state.website_url if hasattr(state, 'website_url') and state.website_url else "https://example.com"
            logger.info(f"Using website URL for interactive element selection: {website_url}")
            selected_element = select_element_interactively(website_url)

            if selected_element:
                # Convert the selected element to the format expected by the application
                element = {
                    'name': selected_element.get('tagName', '') + (f"#{selected_element.get('id')}" if selected_element.get('id') else ""),
                    'tag': selected_element.get('tagName', ''),
                    'selector': selected_element.get('cssSelector', ''),
                    'xpath': selected_element.get('xpath', ''),
                    'attributes': {
                        'id': selected_element.get('id', ''),
                        'name': selected_element.get('name', ''),
                        'class': selected_element.get('className', ''),
                        'type': selected_element.get('type', ''),
                        'value': selected_element.get('value', ''),
                        'placeholder': selected_element.get('placeholder', ''),
                        'href': selected_element.get('href', ''),
                        'role': selected_element.get('role', ''),
                        'aria-label': selected_element.get('ariaLabel', ''),
                        'text': selected_element.get('text', '')
                    },
                    'interactive': True,
                    'visible': True,
                    'manually_selected': True,
                    'score': 100  # Give manually selected elements the highest score
                }

                # Store the element in state manager
                if not hasattr(state, 'detected_elements'):
                    state.detected_elements = []

                # Add the manually selected element to the beginning of the list
                state.detected_elements.insert(0, element)

                # Apply QA-specific filtering (though we'll keep the manually selected element)
                qa_elements = [element]  # Start with the manually selected element

                # Add any other elements that match the filtering criteria
                if hasattr(state, 'detected_elements') and len(state.detected_elements) > 1:
                    other_qa_elements = filter_qa_relevant_elements(
                        state.detected_elements[1:],  # Skip the first element which is our manually selected one
                        locator_strategy=locator_strategy,
                        locator_value=locator_value
                    )
                    qa_elements.extend(other_qa_elements)

                state.qa_relevant_elements = qa_elements

                # Show success message with element information
                st.success(f"✓ Element selected: {element['name']} ({element['selector']})")

                # Perform locator resolution and get results
                resolution_result = _perform_locator_resolution(state, element, selected_step_table_entry, selected_original_step)

                # Display locator resolution results (nested mode to avoid expander conflicts)
                step_info = {
                    'step_no': str(selected_original_step.get('Step No')),
                    'action': selected_step_table_entry.get('action', 'Unknown')
                }
                render_locator_resolution_display(resolution_result, element, step_info, use_expanders=False)

                # Show the selected element details in a container for technical details
                st.markdown("---")
                st.markdown("#### 🔧 Technical Element Details")
                with st.container():
                    st.json(element)

                # Automatically create element matches for the manually selected element
                # This allows skipping the element matching step (4c)
                _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step)
            else:
                st.warning("No element was selected or the selection timed out.")
        except Exception as e:
            st.error(f"Error during interactive element selection: {e}")
            import traceback
            st.error(traceback.format_exc())


def _perform_locator_resolution(state, element, selected_step_table_entry, selected_original_step):
    """
    Perform locator resolution for the selected element and display results.

    Args:
        state: StateManager instance
        element: Selected element data
        selected_step_table_entry: Step table entry with locator information
        selected_original_step: Original step data

    Returns:
        dict: Resolution result from resolve_locator_conflicts()
    """
    try:
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Prepare step data for resolution
        step_data = {
            'locator_strategy': selected_step_table_entry.get('locator_strategy', ''),
            'locator': selected_step_table_entry.get('locator', ''),
            'action': selected_step_table_entry.get('action', ''),
            'step_no': step_no
        }

        # Create element match data for resolution
        element_matches = [{
            'element': element,
            'score': 1.0,
            'manually_selected': True,
            'locator_strategy': 'css',  # Default, will be resolved
            'locator': element.get('selector', '')
        }]

        # Perform locator conflict resolution
        resolution_result = resolve_locator_conflicts(
            step_data=step_data,
            element_matches=element_matches,
            step_no=step_no,
            test_case_id=test_case_id
        )

        # Store resolution results in state
        if not hasattr(state, 'locator_resolution_results'):
            state.locator_resolution_results = {}

        if test_case_id not in state.locator_resolution_results:
            state.locator_resolution_results[test_case_id] = {}

        state.locator_resolution_results[test_case_id][step_no] = resolution_result

        debug("Locator resolution completed",
              stage="stage4", operation="locator_resolution",
              context={
                  'test_case_id': test_case_id,
                  'step_no': step_no,
                  'resolved_strategy': resolution_result.get('resolved_locator_strategy'),
                  'confidence_score': resolution_result.get('confidence_score'),
                  'conflict_detected': resolution_result.get('conflict_detected')
              })

        return resolution_result

    except Exception as e:
        debug(f"Error during locator resolution: {e}",
              stage="stage4", operation="locator_resolution_error",
              context={
                  'test_case_id': test_case_id,
                  'step_no': step_no,
                  'error_type': type(e).__name__
              })

        # Return a fallback resolution result
        return {
            'resolved_locator_strategy': 'css',
            'resolved_locator': element.get('selector', ''),
            'resolution_reason': f'Error during resolution: {str(e)}',
            'confidence_score': 0.5,
            'original_step_locator': step_data,
            'original_element_matches': element_matches,
            'conflict_detected': False
        }


def _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step):
    """Create element match for manually selected element for the selected test case step."""
    try:
        # Prepare the test case and step for automatic matching
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Create a match entry for the manually selected element
        element_match = {
            'element': element,
            'action': selected_step_table_entry.get('action', 'interact with'),
            'score': 1.0,  # Perfect match score
            'reasoning': 'This element was manually selected by the user.',
            'manually_selected': True
        }

        # Create the element matches structure
        if not hasattr(state, 'element_matches'):
            state.element_matches = {}

        if test_case_id not in state.element_matches:
            state.element_matches[test_case_id] = {}

        state.element_matches[test_case_id][step_no] = [element_match]
        state.step_matches = state.element_matches

        # Set the LLM step analysis
        # Analyze if the step requires test data
        test_data_analysis = analyze_step_for_test_data(
            selected_step_table_entry,
            selected_original_step.get('Test Steps')
        )

        # Store the complete analysis in session state
        state.llm_step_analysis = {
            "requires_ui_element": True,
            "reason": "Manually selected UI element will be used for this step.",
            "matches": [element_match],
            "requires_test_data": test_data_analysis["requires_test_data"],
            "test_data_reason": test_data_analysis["reason"],
            "data_types": test_data_analysis["data_types"]
        }

        # Store test data analysis separately for easier access
        state.test_data_analysis = test_data_analysis

        # Automatically set test_data_skipped flag for navigation steps
        if not test_data_analysis["requires_test_data"]:
            state.test_data_skipped = True
            state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
            st.success("✓ Test data configuration automatically skipped for this navigation test case step.")
            st.info("You can proceed directly to Application Stage 6 to generate the test script.")

        # Inform the user that element matching is complete
        if not test_data_analysis["requires_test_data"]:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed directly to Application Stage 6.")

            # Automatically advance to Stage 6 since no test data is needed
            if state.current_stage == StateStage.STAGE4_DETECT:
                state.advance_to(StateStage.STAGE6_GENERATE, "Element matching completed, no test data needed - advancing to Stage 6")

                # Force state update in session state
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 6 (no test data needed)."

                # Call st.rerun() to immediately refresh the UI
                st.rerun()
                return
        else:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed to Application Stage 5.")

            # Automatically advance to Stage 5 for test data configuration
            if state.current_stage == StateStage.STAGE4_DETECT:
                state.advance_to(StateStage.STAGE5_DATA, "Element matching completed - advancing to Stage 5 for test data")

                # Force state update in session state
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 5 for test data configuration."

                # Call st.rerun() to immediately refresh the UI
                st.rerun()
                return
    except Exception as e:
        st.warning(f"Could not automatically complete element matching: {e}. Please use the 'Match Elements with Step' button in Application Stage 4c.")



def _handle_element_matching(state, selected_step_table_entry, selected_original_step):
    """Handle element matching for the selected test case step."""
    with st.expander("Element Matching", expanded=True):
        # Check if automatic stage progression has already been handled
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Check if we already have element matches for this step
        has_existing_matches = (
            hasattr(state, 'element_matches') and
            state.element_matches and
            test_case_id in state.element_matches and
            step_no in state.element_matches[test_case_id]
        )

        # Check if we already have a manually selected element with matches
        has_manual_selection = False
        if has_existing_matches:
            has_manual_selection = any(match.get('manually_selected', False)
                                     for match in state.element_matches[test_case_id][step_no])

        if has_manual_selection:
            st.success("✓ Element manually selected")

            # Show the manually selected element with locator resolution
            if hasattr(state, 'detected_elements') and state.detected_elements:
                for element in state.detected_elements:
                    if element.get('manually_selected', False):
                        # Check if we have locator resolution results for this step
                        if (hasattr(state, 'locator_resolution_results') and
                            test_case_id in state.locator_resolution_results and
                            step_no in state.locator_resolution_results[test_case_id]):

                            resolution_result = state.locator_resolution_results[test_case_id][step_no]
                            step_info = {
                                'step_no': step_no,
                                'action': selected_step_table_entry.get('action', 'Unknown')
                            }
                            render_locator_resolution_summary(resolution_result)
                        else:
                            st.markdown("**Selected Element Details:**")
                            st.json(element)
                        break

        # Check if this is a navigation step that doesn't require UI elements
        elif _is_navigation_step_or_no_ui_required(state, selected_step_table_entry):
            # Check if automatic progression has already been handled
            if has_existing_matches:
                st.success("✓ Navigation step - already processed")

                # Show guidance based on test data requirements
                if hasattr(state, 'test_data_analysis') and state.test_data_analysis:
                    if not state.test_data_analysis["requires_test_data"]:
                        st.info("✓ Ready for Stage 6 (no test data needed)")
                    else:
                        st.info("✓ Ready for Stage 5 (test data configuration)")
            else:
                st.success("✓ Navigation step - no elements needed")
                st.info("✓ Step processed automatically - check stage progression")

        # Check if element detection is required but not done
        elif _requires_ui_elements_but_not_detected(state, selected_step_table_entry):
            st.warning("⚠️ Please select UI elements first using interactive selection")
        else:
            # Prepare for element matching
            if hasattr(state, 'qa_relevant_elements') and state.qa_relevant_elements:
                elements_for_matching = state.qa_relevant_elements
                element_count = len(elements_for_matching)
                st.info(f"Found {element_count} QA-relevant elements")
            elif hasattr(state, 'detected_elements') and state.detected_elements:
                elements_for_matching = state.detected_elements
                element_count = len(elements_for_matching)
                st.info(f"Found {element_count} elements")
            else:
                st.warning("⚠️ No elements detected")
                return

            state.step_elements = elements_for_matching

            # Button to analyze the selected step with detected elements
            # Disable the button if UI elements are required but not detected or if we already have a manual selection
            button_disabled = _requires_ui_elements_but_not_detected(state, selected_step_table_entry) or has_manual_selection

            # Use a single column for the match button to make it more prominent
            match_button = st.button(
                "🔄 Match Elements with Step",
                disabled=button_disabled,
                key="match_elements_btn",
                help="Match UI elements with the selected step" if not has_manual_selection else "Not needed - element already selected",
                use_container_width=True
            )

            if match_button:
                _perform_element_matching(state, selected_step_table_entry, selected_original_step, elements_for_matching)


def _perform_element_matching(state, selected_step_table_entry, selected_original_step, elements_for_matching):
    """Perform element matching using AI for the selected test case step."""
    if _requires_ui_elements_but_not_detected(state, selected_step_table_entry):
        st.error("Please select UI elements first using the 'Select Element Interactively' button above")
    else:
        with st.spinner("Analyzing test case step and matching elements..."):
            try:
                # Prepare the test case and step for analysis
                # Use both the original step and the step table entry
                test_case_for_analysis = {
                    "id": state.selected_test_case.get('Test Case ID'),
                    "objective": state.selected_test_case.get('Test Case Objective'),
                    "steps": [{
                        "step_no": str(selected_original_step.get('Step No')),
                        "action": selected_original_step.get('Test Steps'),
                        "expected": selected_original_step.get('Expected Result'),
                        # Add step table information
                        "step_type": selected_step_table_entry.get('step_type'),
                        "locator_strategy": selected_step_table_entry.get('locator_strategy'),
                        "locator": selected_step_table_entry.get('locator'),
                        "test_data_param": selected_step_table_entry.get('test_data_param'),
                        "assertion_type": selected_step_table_entry.get('assertion_type')
                    }]
                }

                # Match elements with the selected step using context-aware analysis
                use_ai_matching = getattr(state, 'use_ai_matching', True)

                # Check if match_elements_with_ai function is available
                if 'match_elements_with_ai' in globals() and callable(match_elements_with_ai) and use_ai_matching:
                    logger.info("Using AI-powered element matching")
                    element_matches = match_elements_with_ai(
                        test_case_for_analysis,
                        elements_for_matching,
                        state.google_api_key
                    )
                else:
                    st.error("AI element matching function is not available. Please check the core.element_matching module.")
                    element_matches = {}

                if not element_matches or not isinstance(element_matches, dict):
                    st.warning("No valid element matches returned from analysis.")
                    state.step_matches = {}
                    state.element_matches = {}
                    state.llm_step_analysis = {}
                else:
                    # Store matches in state manager
                    state.step_matches = element_matches
                    state.element_matches = element_matches

                    # Update JSON storage with element matching results
                    try:
                        current_step_data = state.get_effective_step_table()
                        # Find and update the current step with element matching data
                        step_no = str(selected_original_step.get('Step No'))
                        for step in current_step_data:
                            if str(step.get('step_no')) == step_no:
                                step['_element_matches'] = element_matches.get(tc_id, {}).get(step_no, [])
                                step['_element_matching_completed'] = True
                                step['_element_matching_timestamp'] = datetime.now().isoformat()
                                break

                        # Save updated step data to JSON
                        state.update_step_data_in_json(current_step_data, f"element_matching_step_{step_no}", {
                            'step_no': step_no,
                            'element_matches_count': len(element_matches.get(tc_id, {}).get(step_no, [])),
                            'requires_ui_element': bool(element_matches.get(tc_id, {}).get(step_no, []))
                        })
                        logger.info(f"✅ Updated JSON storage with element matching results for step {step_no}")
                    except Exception as e:
                        logger.error(f"❌ Failed to update JSON storage with element matching results: {e}")

                    st.success("✓ Element matching completed")

                    # Extract LLM step analysis info from the result
                    tc_id = test_case_for_analysis["id"]
                    step_no = test_case_for_analysis["steps"][0]["step_no"]
                    step_result = element_matches.get(tc_id, {}).get(str(step_no), [])
                    requires_ui_element = bool(step_result)

                    # Analyze if the step requires test data
                    test_data_analysis = analyze_step_for_test_data(
                        selected_step_table_entry,
                        selected_original_step.get('Test Steps')
                    )

                    # Store the complete analysis in state manager
                    state.llm_step_analysis = {
                        "requires_ui_element": requires_ui_element,
                        "reason": "Matched UI element(s) found." if requires_ui_element else "No relevant UI element required for this step.",
                        "matches": step_result,
                        "requires_test_data": test_data_analysis["requires_test_data"],
                        "test_data_reason": test_data_analysis["reason"],
                        "data_types": test_data_analysis["data_types"]
                    }

                    # Store test data analysis separately for easier access
                    state.test_data_analysis = test_data_analysis

                    # Automatically set test_data_skipped flag for navigation steps
                    if not test_data_analysis["requires_test_data"]:
                        state.test_data_skipped = True
                        state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
                        state.step_ready_for_script = True  # Mark the step as ready for script generation
                        st.success("✓ Test data configuration automatically skipped for this navigation test case step.")
                        st.info("You can proceed directly to Application Stage 6 to generate the test script.")

                        # Automatically advance to Stage 6 since no test data is needed
                        if state.current_stage == StateStage.STAGE4_DETECT:
                            state.advance_to(StateStage.STAGE6_GENERATE, "AI element matching completed, no test data needed - advancing to Stage 6")

                            # Force state update in session state
                            st.session_state['state'] = state
                            st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 6 (no test data needed)."

                            # Call st.rerun() to immediately refresh the UI
                            st.rerun()
                            return
                    else:
                        # Automatically advance to Stage 5 for test data configuration
                        if state.current_stage == StateStage.STAGE4_DETECT:
                            state.advance_to(StateStage.STAGE5_DATA, "AI element matching completed - advancing to Stage 5 for test data")

                            # Force state update in session state
                            st.session_state['state'] = state
                            st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 5 for test data configuration."

                            # Call st.rerun() to immediately refresh the UI
                            st.rerun()
                            return

                    # Display matches
                    _display_element_matches(state, tc_id, step_no, step_result, selected_step_table_entry)
            except Exception as e:
                st.error(f"Error during LLM analysis or element matching: {e}")


def _display_previous_step_context(state, selected_step):
    """Display context from previous steps to maintain continuity in the workflow."""
    # Check if we have any completed steps and step context
    if (hasattr(state, 'completed_steps') and state.completed_steps and
        hasattr(state, 'step_context') and state.step_context):

        # Get the current step number
        current_step_no = str(selected_step.get('Step No'))

        # Find previous steps that have been completed
        previous_steps = [step for step in state.completed_steps if step != current_step_no]

        if previous_steps:
            with st.expander("Previous Steps Context", expanded=False):
                # Create tabs for each previous step
                if len(previous_steps) > 1:
                    tabs = st.tabs([f"Step {step}" for step in previous_steps])

                    for i, step_no in enumerate(previous_steps):
                        with tabs[i]:
                            _display_step_context_content(state, step_no)
                else:
                    # Just one previous step
                    _display_step_context_content(state, previous_steps[0])


def _display_step_context_content(state, step_no):
    """Helper function to display the content of a step context."""
    if step_no in state.step_context:
        step_ctx = state.step_context[step_no]

        # Show elements if available
        if step_ctx.get("elements"):
            st.markdown(f"**UI Elements:** {len(step_ctx['elements'])} elements")

        # Show test data if available
        if step_ctx.get("test_data"):
            st.markdown("**Test Data:**")
            for key, value in step_ctx["test_data"].items():
                st.markdown(f"- {key}: `{value}`")

        # Show script path if available
        if step_ctx.get("script_path"):
            st.markdown(f"**Script:** {os.path.basename(step_ctx['script_path'])}")
    else:
        st.info(f"No context for Step {step_no}")


def _display_element_matches(state, tc_id, step_no, step_result, selected_step_table_entry):
    """
    Display element matches in an organized and user-friendly format.
    """
    st.markdown("**Element Matches:**")

    # Get locator strategy from step table entry
    locator_strategy = None
    locator_value = None
    if selected_step_table_entry:
        locator_strategy = selected_step_table_entry.get('locator_strategy')
        locator_value = selected_step_table_entry.get('locator')

    if step_result:
        for i, match in enumerate(step_result):
            element = match.get('element', {})
            element_name = element.get('name', 'Unknown')
            selector = element.get('selector', 'Unknown')
            action = match.get('action', 'Unknown')

            # Check if element matches the locator strategy
            locator_match = False
            if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                attrs = element.get('attributes', {})
                strategy_to_attribute = {
                    'id': 'id', 'name': 'name', 'css': 'selector', 'xpath': 'xpath',
                    'tag': 'tag', 'class': 'class', 'link_text': 'text',
                    'partial_link_text': 'text', 'aria': 'role'
                }

                attribute = strategy_to_attribute.get(locator_strategy.lower())
                if attribute:
                    if attribute in ['selector', 'xpath']:
                        locator_match = True
                    else:
                        attr_value = attrs.get(attribute, '')
                        if attr_value:
                            locator_match = True
                            if locator_value:
                                if locator_strategy.lower() == 'partial_link_text':
                                    locator_match = locator_value.lower() in attr_value.lower()
                                else:
                                    locator_match = attr_value.lower() == locator_value.lower()

            # Display element with match indicator
            match_indicator = f"✅ *{locator_strategy}*" if locator_match else ""
            st.markdown(f"**Match {i+1}:** {element_name} {match_indicator}")
            st.markdown(f"- Action: {action}")

            # Show confidence score if available
            if 'score' in match:
                score = match.get('score', 0)
                st.progress(score)
                st.markdown(f"- Confidence: {score:.2f}")

            # Show reasoning if available
            if 'reasoning' in match:
                st.markdown("**Reasoning:**")
                st.markdown(match.get('reasoning'))
    else:
        st.info("No UI element required for this step.")

    # Show warning if no elements match the locator strategy
    if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', ''] and step_result:
        matches_locator = any(
            match.get('element', {}).get('locator_strategy_match', False)
            for match in step_result
        )
        if not matches_locator:
            st.warning(f"⚠️ No elements match locator: {locator_strategy}")
